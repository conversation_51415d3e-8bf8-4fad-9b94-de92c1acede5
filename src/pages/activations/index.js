import React, { useState, useEffect, useCallback, useMemo } from 'react';
import axios from 'axios';
import { Disclosure } from '@headlessui/react'; // Import Disclosure
import { FunnelIcon, XMarkIcon } from '@heroicons/react/24/outline'; // Import icons
import config from "../../../config.json"

// --- Reusable Helper Functions ---
const getNested = (obj, path, defaultValue = undefined) => {
  // (Keep the existing getNested function)
  try {
    if (!path || typeof path !== 'string' || !obj) {
      return defaultValue;
    }
    const value = path.split('.').reduce((o, k) => (o && o[k] !== undefined && o[k] !== null) ? o[k] : undefined, obj);
    return (value === undefined) ? defaultValue : value;
  } catch (e) {
    console.error("Error in getNested:", e, "Path:", path, "Object Type:", typeof obj);
    return defaultValue;
  }
};

const DeactivationModal = ({ isOpen, onClose, onConfirm, creditLineName }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-sm mx-auto">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Confirm Deactivation</h3>
        <p className="text-sm text-gray-500 mb-6">
          Are you sure you want to deactivate the credit line for <span className="font-medium">{creditLineName}</span>? This will change the status to SUSPENDED.
        </p>
        <div className="flex justify-end space-x-4">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200"
          >
            Cancel
          </button>
          <button
            onClick={onConfirm}
            className="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700"
          >
            Deactivate
          </button>
        </div>
      </div>
    </div>
  );
};

// --- Reusable Helper Functions --- (Keep getNested)
// --- Imports --- (Keep React, useState, Disclosure, Icons etc.)

// PASTE THIS NEW FilterSection COMPONENT DEFINITION
const FilterSection = ({ filters, setFilters, resetFilters }) => {
  // Common input styling
  const inputBaseClass = "block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm";
  const numberInputClass = `${inputBaseClass} px-2 py-1`;
  const dateInputClass = `${inputBaseClass} px-2 py-1`;
  const textInputClass = `${inputBaseClass} px-3 py-1.5`;

  const handleFilterChange = (event) => {
    const { name, value } = event.target;
    setFilters(prevFilters => ({
      ...prevFilters,
      [name]: value,
    }));
  };

  // Calculate the number of active filters
  const activeFilterCount = useMemo(() => {
    return Object.values(filters).filter(value => value !== '' && value !== null && value !== undefined).length;
  }, [filters]);

  const handleReset = (event) => {
    event.stopPropagation(); // Prevent the disclosure from toggling
    resetFilters();
  }

  return (
    // Add margin below the whole filter section
    <div className="mb-6">
      <Disclosure as="div" className="border border-gray-200 rounded-lg shadow-sm bg-white">
        {({ open }) => (
          <>
            {/* The single Toggable Header Bar - Styled to match example */}
            <div className="flow-root">
              <Disclosure.Button className="flex w-full items-center justify-between px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus-visible:ring focus-visible:ring-indigo-500 focus-visible:ring-opacity-75">
                {/* Left side: Icon, Text, Count */}
                <span className="flex items-center">
                  <FunnelIcon className="mr-2 h-5 w-5 text-gray-400" aria-hidden="true" />
                  Filters
                  {activeFilterCount > 0 && (
                    <span className="ml-2 rounded-full bg-gray-200 px-2 py-0.5 text-xs font-medium text-gray-800">
                      {activeFilterCount}
                    </span>
                  )}
                </span>

                {/* Right side: Clear button OR Expand/Collapse Icon - Example implicitly uses the whole bar click */}
                <span className="ml-6 flex items-center">
                  {/* Optional: Show Chevron only if preferred over whole bar click */}
                  {/* <ChevronUpIcon
                        className={`${open ? 'rotate-180 transform' : ''} h-5 w-5 text-gray-500 transition-transform duration-150 ease-in-out`}
                    /> */}
                </span>
              </Disclosure.Button>
            </div>

            {/* Separator Line for the Panel */}
            {open && <div className="border-t border-gray-200"></div>}

            {/* The Panel containing all filter inputs */}
            <Disclosure.Panel className="px-4 py-5 sm:px-6 lg:px-8">
              {/* Top row with search and maybe clear button */}
              <div className="mb-4 flex items-start justify-between">
                <div className="flex-1 mr-4"> {/* Search takes most space */}
                  <label htmlFor="searchTerm" className="sr-only">Search (Name)</label>
                  <input
                    type="text"
                    name="searchTerm"
                    id="searchTerm"
                    value={filters.searchTerm}
                    onChange={handleFilterChange}
                    className={textInputClass}
                    placeholder="Search Borrower or Business..."
                  />
                </div>
                {/* Clear button moved next to search */}
                <button
                  type="button"
                  onClick={handleReset} // Use the wrapper function
                  className="inline-flex items-center justify-center rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                >
                  <XMarkIcon className="-ml-1 mr-1.5 h-4 w-4 text-gray-400" aria-hidden="true" />
                  Clear all
                </button>
              </div>

              {/* Grid for the rest of the filters */}
              <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">

                {/* Offer Limit Range */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Offer Limit (QAR)</label>
                  <div className="flex space-x-2">
                    <input type="number" name="minLimit" value={filters.minLimit} onChange={handleFilterChange} placeholder="Min" className={numberInputClass} />
                    <input type="number" name="maxLimit" value={filters.maxLimit} onChange={handleFilterChange} placeholder="Max" className={numberInputClass} />
                  </div>
                </div>

                {/* Interest Rate Range */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Service Fee (%)</label>
                  <div className="flex space-x-2">
                    <input type="number" name="minRate" value={filters.minRate} onChange={handleFilterChange} placeholder="Min" step="0.01" className={numberInputClass} />
                    <input type="number" name="maxRate" value={filters.maxRate} onChange={handleFilterChange} placeholder="Max" step="0.01" className={numberInputClass} />
                  </div>
                </div>

                {/* Tenure Range */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Tenure (Days)</label>
                  <div className="flex space-x-2">
                    <input type="number" name="minTenure" value={filters.minTenure} onChange={handleFilterChange} placeholder="Min" step="1" className={numberInputClass} />
                    <input type="number" name="maxTenure" value={filters.maxTenure} onChange={handleFilterChange} placeholder="Max" step="1" className={numberInputClass} />
                  </div>
                </div>

                {/* Date Range */}
                <div className="sm:col-span-2 md:col-span-1"> {/* Adjust span as needed */}
                  <label className="block text-sm font-medium text-gray-700 mb-1">Last Updated Date</label>
                  <div className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:items-center sm:space-x-2">
                    <input type="date" name="startDate" value={filters.startDate} onChange={handleFilterChange} className={dateInputClass + ' flex-1'} />
                    <span className="text-gray-500 text-center hidden sm:inline">to</span>
                    <input type="date" name="endDate" value={filters.endDate} onChange={handleFilterChange} className={dateInputClass + ' flex-1'} />
                  </div>
                </div>
              </div>
            </Disclosure.Panel>
          </>
        )}
      </Disclosure>
      {/* REMOVED ActiveFilterTags component - it's not in the target UI example */}
    </div>
  );
};
// END OF NEW FilterSection COMPONENT


// REMOVE the ActiveFilterTags component definition entirely from your file
// const ActiveFilterTags = ({ filters, setFilters }) => { ... }; // DELETE THIS


// --- formatDate / getNested --- (Keep as is)

// --- CreditLineActivationPage ---
// REMEMBER: You still need the changes inside CreditLineActivationPage mentioned before:
// 1. REMOVE the `showFilters` state variable.
// 2. REMOVE the `showFilters` and `setShowFilters` props passed to `<FilterSection>`.
// 3. The `<FilterSection>` call should just be:
//    <FilterSection filters={filters} setFilters={setFilters} resetFilters={resetFilters} />
//    (The margin `mb-6` is now handled *inside* the FilterSection component's wrapper div).

// Component to display applied filters as tags
// PASTE THIS NEW ActiveFilterTags COMPONENT DEFINITION
// const ActiveFilterTags = ({ filters, setFilters }) => {
//   // Check if any filter is active
//   const hasActiveFilters = Object.entries(filters).some(([key, value]) => {
//     if (typeof value === 'string') return value.trim() !== '';
//     return value !== '';
//   });

//   if (!hasActiveFilters) return null;

//   // Function to remove a single filter
//   const removeFilter = (filterKey) => {
//     setFilters(prev => ({ ...prev, [filterKey]: '' }));
//   };

//   return (
//     <div className="bg-gray-50 py-3 px-4 sm:px-6 lg:px-8 border-t border-gray-200 rounded-b-lg"> {/* Adjusted styling slightly */}
//       <div className="flex flex-wrap items-center gap-2">
//         <span className="text-sm font-medium text-gray-700">Active filters:</span>

//         {filters.searchTerm && (
//           <span className="inline-flex items-center rounded-full bg-white px-2.5 py-1 text-xs font-medium text-gray-700 ring-1 ring-inset ring-gray-200"> {/* Adjusted tag style */}
//             Search: {filters.searchTerm}
//             <button
//               onClick={() => removeFilter('searchTerm')}
//               className="-mr-1 ml-1.5 inline-flex flex-shrink-0 text-gray-400 hover:text-gray-500 focus:outline-none"
//             >
//               <span className="sr-only">Remove filter</span>
//               <XMarkIcon className="h-3 w-3" />
//             </button>
//           </span>
//         )}

//         {/* Repeat similar span structure for all other filters... */}
//         {/* Example for minLimit */}
//         {filters.minLimit && (
//           <span className="inline-flex items-center rounded-full bg-white px-2.5 py-1 text-xs font-medium text-gray-700 ring-1 ring-inset ring-gray-200">
//             Min Limit: {filters.minLimit}
//             <button onClick={() => removeFilter('minLimit')} className="-mr-1 ml-1.5 inline-flex flex-shrink-0 text-gray-400 hover:text-gray-500 focus:outline-none">
//               <span className="sr-only">Remove filter</span>
//               <XMarkIcon className="h-3 w-3" />
//             </button>
//           </span>
//         )}
//         {/* ... and maxLimit, minRate, maxRate, minTenure, maxTenure, startDate, endDate */}
//         {filters.maxLimit && (
//           <span className="inline-flex items-center rounded-full bg-white px-2.5 py-1 text-xs font-medium text-gray-700 ring-1 ring-inset ring-gray-200">
//             Max Limit: {filters.maxLimit}
//             <button onClick={() => removeFilter('maxLimit')} className="-mr-1 ml-1.5 inline-flex flex-shrink-0 text-gray-400 hover:text-gray-500 focus:outline-none">
//               <span className="sr-only">Remove filter</span><XMarkIcon className="h-3 w-3" />
//             </button>
//           </span>
//         )}
//         {filters.minRate && (
//           <span className="inline-flex items-center rounded-full bg-white px-2.5 py-1 text-xs font-medium text-gray-700 ring-1 ring-inset ring-gray-200">
//             Min Rate: {filters.minRate}%
//             <button onClick={() => removeFilter('minRate')} className="-mr-1 ml-1.5 inline-flex flex-shrink-0 text-gray-400 hover:text-gray-500 focus:outline-none">
//               <span className="sr-only">Remove filter</span><XMarkIcon className="h-3 w-3" />
//             </button>
//           </span>
//         )}
//         {filters.maxRate && (
//           <span className="inline-flex items-center rounded-full bg-white px-2.5 py-1 text-xs font-medium text-gray-700 ring-1 ring-inset ring-gray-200">
//             Max Rate: {filters.maxRate}%
//             <button onClick={() => removeFilter('maxRate')} className="-mr-1 ml-1.5 inline-flex flex-shrink-0 text-gray-400 hover:text-gray-500 focus:outline-none">
//               <span className="sr-only">Remove filter</span><XMarkIcon className="h-3 w-3" />
//             </button>
//           </span>
//         )}
//         {filters.minTenure && (
//           <span className="inline-flex items-center rounded-full bg-white px-2.5 py-1 text-xs font-medium text-gray-700 ring-1 ring-inset ring-gray-200">
//             Min Tenure: {filters.minTenure} days
//             <button onClick={() => removeFilter('minTenure')} className="-mr-1 ml-1.5 inline-flex flex-shrink-0 text-gray-400 hover:text-gray-500 focus:outline-none">
//               <span className="sr-only">Remove filter</span><XMarkIcon className="h-3 w-3" />
//             </button>
//           </span>
//         )}
//         {filters.maxTenure && (
//           <span className="inline-flex items-center rounded-full bg-white px-2.5 py-1 text-xs font-medium text-gray-700 ring-1 ring-inset ring-gray-200">
//             Max Tenure: {filters.maxTenure} days
//             <button onClick={() => removeFilter('maxTenure')} className="-mr-1 ml-1.5 inline-flex flex-shrink-0 text-gray-400 hover:text-gray-500 focus:outline-none">
//               <span className="sr-only">Remove filter</span><XMarkIcon className="h-3 w-3" />
//             </button>
//           </span>
//         )}
//         {filters.startDate && (
//           <span className="inline-flex items-center rounded-full bg-white px-2.5 py-1 text-xs font-medium text-gray-700 ring-1 ring-inset ring-gray-200">
//             From: {filters.startDate}
//             <button onClick={() => removeFilter('startDate')} className="-mr-1 ml-1.5 inline-flex flex-shrink-0 text-gray-400 hover:text-gray-500 focus:outline-none">
//               <span className="sr-only">Remove filter</span><XMarkIcon className="h-3 w-3" />
//             </button>
//           </span>
//         )}
//         {filters.endDate && (
//           <span className="inline-flex items-center rounded-full bg-white px-2.5 py-1 text-xs font-medium text-gray-700 ring-1 ring-inset ring-gray-200">
//             To: {filters.endDate}
//             <button onClick={() => removeFilter('endDate')} className="-mr-1 ml-1.5 inline-flex flex-shrink-0 text-gray-400 hover:text-gray-500 focus:outline-none">
//               <span className="sr-only">Remove filter</span><XMarkIcon className="h-3 w-3" />
//             </button>
//           </span>
//         )}

//       </div>
//     </div>
//   );
// };
// END OF NEW ActiveFilterTags COMPONENT

const formatDate = (dateString) => {
  // (Keep the existing formatDate function)
  if (!dateString || dateString === 'N/A' || dateString === undefined) return 'N/A';
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime()) || date.getFullYear() <= 1970) return 'N/A';
    return date.toLocaleDateString('en-GB', { day: '2-digit', month: 'short', year: 'numeric' });
  } catch (e) {
    console.error("Error formatting date:", dateString, e);
    return 'Invalid Date';
  }
};
// --- End Helper Functions ---

// =========================================================================
//  MAIN CREDIT LINE ACTIVATION PAGE COMPONENT
// =========================================================================
export default function CreditLineActivationPage() {
  // --- State Variables ---
  const [allActivations, setAllActivations] = useState([]);
  const [pendingActivations, setPendingActivations] = useState([]);
  const [activeActivations, setActiveActivations] = useState([]);

  const [loading, setLoading] = useState(true);
  const [fetchError, setFetchError] = useState(null);
  const [activeTab, setActiveTab] = useState('all');
  const [activatingId, setActivatingId] = useState(null);
  const [deactivatingId, setDeactivatingId] = useState(null);
  const [showDeactivateModal, setShowDeactivateModal] = useState(false);
  const [selectedCreditLine, setSelectedCreditLine] = useState(null);


  const openDeactivateModal = (creditLine) => {
    setSelectedCreditLine(creditLine);
    setShowDeactivateModal(true);
  };


  // --- Data Filtering Logic for Tabs ---
  const filterActivationTabs = useCallback((activations) => {
    setPendingActivations(activations.filter(a => a.creditLineStatus === 'APPROVED' || a.creditLineStatus === 'SUSPENDED'));
    setActiveActivations(activations.filter(a => a.creditLineStatus === 'ACTIVE'));
  }, []);
  // --- State for Filters ---
  const [filters, setFilters] = useState({
    searchTerm: '',
    minLimit: '',
    maxLimit: '',
    minRate: '',
    maxRate: '',
    minTenure: '',
    maxTenure: '',
    startDate: '',
    endDate: '',
  });
  // const [showFilters, setShowFilters] = useState(false);

  const resetFilters = React.useCallback(() => { // Assuming React is imported
    setFilters({
      searchTerm: '', minLimit: '', maxLimit: '', minRate: '', maxRate: '',
      minTenure: '', maxTenure: '', startDate: '', endDate: '',
    });
  }, []);



  // --- Filter Input Change Handler ---
  // const handleFilterChange = (event) => {
  //   const { name, value } = event.target;
  //   setFilters(prevFilters => ({
  //     ...prevFilters,
  //     [name]: value,
  //   }));
  // };

  // --- Data Fetching Function ---
  const fetchData = useCallback(async () => {
    console.log("fetchData called for Activation Page");
    setLoading(true);
    setFetchError(null);
    // Don't clear individual tab states here, filterActivationTabs will do it

    try {
      const loggedInLenderId = localStorage.getItem('userId');
      if (!loggedInLenderId) throw new Error("Lender ID not found.");

      const [clResult, userResult, offerResult] = await Promise.allSettled([
        axios.get(`${config.apiUrl}/ops/invoiceFinancing/fetchCreditLines`),
        axios.get(`${config.apiUrl}/ops/invoiceFinancing/getSubmittedKycs`),
        axios.get(`${config.apiUrl}/ops/invoiceFinancing/offers?offerType=creditLineOffer`)
      ]);

      // --- Process Responses ---
      let fetchedCreditLines = [];
      if (clResult.status === 'fulfilled' && clResult.value.data) {
        fetchedCreditLines = Array.isArray(clResult.value.data) ? clResult.value.data : (clResult.value.data.creditLines || []);
      } else { console.error("Failed to fetch credit lines", clResult.reason || clResult.value?.status); }

      let fetchedUsersMap = new Map();
      if (userResult.status === 'fulfilled' && userResult.value.data?.success && Array.isArray(userResult.value.data.kycs)) {
        fetchedUsersMap = new Map(userResult.value.data.kycs.map(user => [user._id, user]));
      } else { console.error("Failed to fetch users", userResult.reason || userResult.value?.status); }

      let fetchedOffers = [];
      if (offerResult.status === 'fulfilled' && offerResult.value.data?.success && Array.isArray(offerResult.value.data.offers)) {
        fetchedOffers = offerResult.value.data.offers;
      } else { console.warn("Failed to fetch offers", offerResult.reason || offerResult.value?.status); }

      // --- Combine and Filter Data ---
      const relevantActivations = [];
      const offersMap = new Map(fetchedOffers.map(o => [`${o.merchantId}-${o.lenderId}`, o]));

      fetchedCreditLines.forEach(cl => {
        const clStatus = String(cl.creditLineStatus || '').toUpperCase();
        const isRelevantStatus = clStatus === 'APPROVED' || clStatus === 'ACTIVE' || clStatus === 'SUSPENDED';
        const isCorrectLender = String(cl.lenderId) === loggedInLenderId;
        const isOfferAccepted = cl.offerAccepted === true;

        if (isRelevantStatus && isCorrectLender && isOfferAccepted) {
          const userDetails = fetchedUsersMap.get(cl.userId);
          const offerDetails = offersMap.get(`${cl.userId}-${cl.lenderId}`);

          if (userDetails && offerDetails) {
            relevantActivations.push({ ...cl, userDetails, offerDetails });
          } else {
            console.warn(`Skipping CL ${cl._id} (Status: ${clStatus}, Lender: ${cl.lenderId}, UserID: ${cl.userId}): Missing -> ${!userDetails ? 'User Details ' : ''}${!offerDetails ? 'Offer Details' : ''}`);
            if (!userDetails) console.warn(` > User Map does not have key: ${cl.userId}`);
            if (!offerDetails) console.warn(` > Offer Map does not have key: ${cl.userId}-${cl.lenderId}`);
          }
        }
      });

      console.log(`Found ${relevantActivations.length} relevant activations for lender ${loggedInLenderId}`);
      setAllActivations(relevantActivations); // Set the master list for this page
      filterActivationTabs(relevantActivations); // Filter into tabs

    } catch (error) {
      console.error('Error during fetchData on Activation Page:', error);
      setFetchError(error.message || "An unknown error occurred.");
      setAllActivations([]);
      filterActivationTabs([]);
    } finally {
      setLoading(false);
    }
  }, [filterActivationTabs]); // Dependency


  const handleDeactivate = useCallback(async (creditLine) => {
    if (!creditLine?._id || !creditLine?.userId) {
      alert("Error: Missing necessary information to deactivate.");
      return;
    }

    setDeactivatingId(creditLine._id);

    const payload = {
      userId: creditLine.userId,
      creditLineData: {
        _id: creditLine._id,
        creditLineStatus: 'SUSPENDED'
      }
    };
    console.log("Deactivation Payload:", JSON.stringify(payload, null, 2));

    try {
      const response = await axios.post(`${config.apiUrl}/ops/invoiceFinancing/creditLineCreateOrUpdate`, payload);

      if (!response.data?.success && !response.data?.creditLine) {
        throw new Error(response.data?.message || "Backend reported failure during deactivation.");
      }

      console.log("Deactivation successful for:", creditLine._id);
      alert("Credit line deactivated successfully!");
      await fetchData(); // Refresh the entire list

    } catch (error) {
      console.error('Error deactivating credit line:', error.response?.data || error.message);
      alert(`Error deactivating credit line: ${getNested(error, 'response.data.message', error.message) || 'An unknown error occurred'}`);
    } finally {
      setDeactivatingId(null);
      setShowDeactivateModal(false);
    }
  }, [fetchData]);

  // --- Initial Data Fetch ---
  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // --- Handle Activate Button Click ---
  const handleActivate = useCallback(async (creditLine) => {
    // (Keep the existing handleActivate function - it's correct)
    if (!creditLine?._id || !creditLine?.userId) {
      alert("Error: Missing necessary information to activate.");
      return;
    }
    if (creditLine.creditLineStatus !== 'APPROVED' && creditLine.creditLineStatus !== 'SUSPENDED') {
      alert("Credit line is not in approved or deactivated status.");
      return;
    }

    setActivatingId(creditLine._id);

    const payload = {
      userId: creditLine.userId,
      creditLineData: {
        _id: creditLine._id,
        creditLineStatus: 'ACTIVE'
      }
    };
    console.log("Activation Payload:", JSON.stringify(payload, null, 2));

    try {
      const response = await axios.post(`${config.apiUrl}/ops/invoiceFinancing/creditLineCreateOrUpdate`, payload);

      if (!response.data?.success && !response.data?.creditLine) {
        throw new Error(response.data?.message || "Backend reported failure during activation.");
      }

      console.log("Activation successful for:", creditLine._id);
      alert("Credit line activated successfully!");
      await fetchData(); // Refresh the entire list

    } catch (error) {
      console.error('Error activating credit line:', error.response?.data || error.message);
      alert(`Error activating credit line: ${getNested(error, 'response.data.message', error.message) || 'An unknown error occurred'}`);
    } finally {
      setActivatingId(null);
    }
  }, [fetchData]);

  const currentTableData = useMemo(() => {
    let sourceData;
    if (activeTab === 'all') {
      sourceData = allActivations;
    } else if (activeTab === 'pending') {
      sourceData = pendingActivations;
    } else { // 'activated'
      sourceData = activeActivations;
    }

    if (!sourceData || sourceData.length === 0) return [];

    const { searchTerm, minLimit, maxLimit, minRate, maxRate, minTenure, maxTenure, startDate, endDate } = filters;

    // Prepare filter values once
    const lowerSearchTerm = searchTerm.toLowerCase();
    const numMinLimit = minLimit === '' ? -Infinity : parseFloat(minLimit);
    const numMaxLimit = maxLimit === '' ? Infinity : parseFloat(maxLimit);
    const numMinRate = minRate === '' ? -Infinity : parseFloat(minRate);
    const numMaxRate = maxRate === '' ? Infinity : parseFloat(maxRate);
    const numMinTenure = minTenure === '' ? -Infinity : parseInt(minTenure, 10);
    const numMaxTenure = maxTenure === '' ? Infinity : parseInt(maxTenure, 10);
    // Get timestamp for start/end of day for date comparison
    const tsStartDate = startDate ? new Date(startDate).setHours(0, 0, 0, 0) : null;
    const tsEndDate = endDate ? new Date(endDate).setHours(23, 59, 59, 999) : null;

    // Filter the source data
    return sourceData.filter(cl => {
      const borrowerName = `${getNested(cl, 'userDetails.firstName', '')} ${getNested(cl, 'userDetails.lastName', '')}`.toLowerCase();
      const businessName = getNested(cl, 'userDetails.kyc.businessDetails.businessName', '').toLowerCase();
      const limit = parseFloat(getNested(cl, 'offerDetails.creditLimit', NaN)); // Default to NaN if missing
      const rate = parseFloat(getNested(cl, 'offerDetails.interestRate', NaN));
      const tenure = parseInt(getNested(cl, 'offerDetails.tenureDays', NaN), 10);
      const updatedAtTs = cl.updatedAt ? new Date(cl.updatedAt).getTime() : null;

      // Apply Filters - return false if ANY filter fails
      if (lowerSearchTerm && !(borrowerName.includes(lowerSearchTerm) || businessName.includes(lowerSearchTerm))) {
        return false;
      }
      if (!isNaN(numMinLimit) && (isNaN(limit) || limit < numMinLimit)) { return false; }
      if (!isNaN(numMaxLimit) && (isNaN(limit) || limit > numMaxLimit)) { return false; }
      if (!isNaN(numMinRate) && (isNaN(rate) || rate < numMinRate)) { return false; }
      if (!isNaN(numMaxRate) && (isNaN(rate) || rate > numMaxRate)) { return false; }
      if (!isNaN(numMinTenure) && (isNaN(tenure) || tenure < numMinTenure)) { return false; }
      if (!isNaN(numMaxTenure) && (isNaN(tenure) || tenure > numMaxTenure)) { return false; }
      if (tsStartDate && (!updatedAtTs || updatedAtTs < tsStartDate)) { return false; }
      if (tsEndDate && (!updatedAtTs || updatedAtTs > tsEndDate)) { return false; }

      return true; // Passed all filters
    });
  }, [filters, activeTab, allActivations, pendingActivations, activeActivations]);

  // --- Function to Render Table Rows ---
  const renderTableRows = (data) => {
    // (Keep the existing renderTableRows function)
    if (!data || data.length === 0) {
      return (
        <tr>
          <td colSpan="7" className="text-center py-10 px-4 text-sm text-gray-500 italic">
            No credit lines found matching the current filters in this section.
          </td>
        </tr>
      );
    }

    return data.map((cl) => {
      const offerDetails = cl.offerDetails || {}; // Use the embedded offer details
      // Correctly get the signedUrl for the contract

      // const contractUrl = getNested(offerDetails, 'invoiceContract.signedUrl');
      console.log(cl.offerDetails?.facilityContract?.signedUrl, "url here")
      const contractUrl = cl.offerDetails?.facilityContract?.signedUrl ?? '/creditLineContract.pdf'
      const isActivatingThis = activatingId === cl._id;

      return (
        <tr key={cl._id} className="hover:bg-gray-50">
          {/* Business Name */}
          <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900">
            {getNested(cl, 'userDetails.kyc.businessDetails.businessName', 'N/A')}
          </td>
          {/* Borrower Name */}
          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
            <div className="font-medium">{[getNested(cl, 'userDetails.firstName'), getNested(cl, 'userDetails.lastName')].filter(Boolean).join(' ')}</div>
            <div className="text-xs text-gray-500">{getNested(cl, 'userDetails.email', 'N/A')}</div>
          </td>
          {/* Borrower Phone */}
          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
            {getNested(cl, 'userDetails.mobileNo', 'N/A')}
          </td>
          {/* Last Updated Date */}
          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
            {formatDate(cl.updatedAt)}
          </td>
          {/* Offer Details */}
          <td className="px-4 py-3 whitespace-normal text-xs text-gray-700 align-top">
            <div className="flex flex-col space-y-0.5">
              <span>Limit: <span className="font-medium">{getNested(offerDetails, 'creditLimit', 'N/A')?.toLocaleString()} {cl.currency || 'QAR'}</span></span>
              <span>Tenure: <span className="font-medium">{getNested(offerDetails, 'tenureDays', 'N/A')} Days</span></span>
              <span>Service Fee: <span className="font-medium">{getNested(offerDetails, 'interestRate', 'N/A')}%</span></span>
            </div>
          </td>
          {/* Contract */}
          <td className="px-4 py-3 whitespace-nowrap text-center text-sm font-medium">
            {contractUrl ? (
              <a
                href={contractUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="text-indigo-600 hover:text-indigo-900 hover:underline inline-flex items-center"
                title="View Contract PDF"
              >
                View
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                  <path strokeLinecap="round" strokeLinejoin="round" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                </svg>
              </a>
            ) : (
              <span className="text-gray-400 italic text-xs">N/A</span>
            )}
          </td>
          {/* Actions */}
          <td className="px-4 py-3 whitespace-nowrap text-center text-sm font-medium">
            {(cl.creditLineStatus === 'APPROVED' || cl.creditLineStatus === 'SUSPENDED') && (
              <button
                onClick={() => handleActivate(cl)}
                disabled={isActivatingThis}
                className={`inline-flex items-center justify-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white
                ${isActivatingThis ? 'bg-gray-400 cursor-not-allowed' : 'bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500'}`}
              >
                {isActivatingThis ? (
                  <>
                    <svg className="animate-spin -ml-0.5 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Activating...
                  </>
                ) : 'Activate'}
              </button>
            )}
            {cl.creditLineStatus === 'ACTIVE' && (
              <button
                onClick={() => openDeactivateModal(cl)}
                disabled={deactivatingId === cl._id}
                className={`inline-flex items-center justify-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white
                                    ${deactivatingId === cl._id ? 'bg-gray-400 cursor-not-allowed' : 'bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500'}`}
              >
                {deactivatingId === cl._id ? (
                  <>
                    <svg className="animate-spin -ml-0.5 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Deactivating...
                  </>
                ) : 'Deactivate'}
              </button>
            )}
          </td>
        </tr>
      );
    });
  };

  // --- Common Input Styling ---
  // const inputBaseClass = "block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm";
  // const numberInputClass = `${inputBaseClass} px-2 py-1`;
  // const dateInputClass = `${inputBaseClass} px-2 py-1`;
  // const textInputClass = `${inputBaseClass} px-3 py-1.5`;

  // --- MAIN RENDER ---
  return (
    <div className="p-4 md:p-6 bg-gray-100 min-h-screen">
      <h1 className="text-2xl font-bold mb-4 text-gray-800">Credit Line Activation</h1>

      {/* Filter Component */}
      <div className="mb-6"> {/* Optional: Add margin below filters */}
        <FilterSection
          filters={filters}
          setFilters={setFilters}
          // No showFilters/setShowFilters needed here
          resetFilters={resetFilters}
        />
      </div>

      {/* Tab Navigation */}
      <div className="mb-6 border-b border-gray-200">
        <nav className="-mb-px flex space-x-6 overflow-x-auto" aria-label="Tabs">
          <button onClick={() => setActiveTab('all')}
            className={`whitespace-nowrap py-3 px-4 border-b-2 font-medium text-sm focus:outline-none
                        ${activeTab === 'all' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}
          >
            All ({loading ? '...' : allActivations.length}) {/* Show total count before filtering */}
          </button>
          <button onClick={() => setActiveTab('pending')}
            className={`whitespace-nowrap py-3 px-4 border-b-2 font-medium text-sm focus:outline-none
                        ${activeTab === 'pending' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}
          >
            Pending Activation ({loading ? '...' : pendingActivations.length})
          </button>
          <button onClick={() => setActiveTab('activated')}
            className={`whitespace-nowrap py-3 px-4 border-b-2 font-medium text-sm focus:outline-none
                        ${activeTab === 'activated' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}
          >
            Activated ({loading ? '...' : activeActivations.length})
          </button>
        </nav>
      </div>

      {/* Loading & Error Display */}
      {loading && !fetchError && (
        <div className="text-center py-10 text-gray-500 italic">Loading activation data...</div>
      )}
      {fetchError && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <strong className="font-bold">Error: </strong>
          <span className="block sm:inline">{fetchError}</span>
          <button onClick={fetchData} className="ml-4 py-1 px-2 border border-red-300 bg-red-100 text-red-700 rounded text-xs hover:bg-red-200">
            Retry Fetch
          </button>
        </div>
      )}

      {/* Table Display */}
      {!loading && !fetchError && (
        <div className="shadow overflow-x-auto border-b border-gray-200 sm:rounded-lg">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Business Name</th>
                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Borrower</th>
                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Phone</th>
                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Updated</th>
                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Offer Details</th>
                <th scope="col" className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Contract</th>
                <th scope="col" className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {/* Use the memoized and filtered data */}
              {renderTableRows(currentTableData)}
            </tbody>
          </table>
        </div>
      )}
      <DeactivationModal
        isOpen={showDeactivateModal}
        onClose={() => setShowDeactivateModal(false)}
        onConfirm={() => handleDeactivate(selectedCreditLine)}
        creditLineName={getNested(selectedCreditLine, 'userDetails.kyc.businessDetails.businessName', 'this credit line')}
      />
    </div>
  );
}