import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { produce } from 'immer'; // Using Immer for easier state updates
import { saveAs } from 'file-saver';
import JSZip from 'jszip'; // <--- ADD THIS IMPORT
import { PDFDocument, StandardFonts, rgb, degrees } from 'pdf-lib';
import { ChevronUpIcon, XMarkIcon, FunnelIcon } from '@heroicons/react/24/outline';
import { Disclosure } from '@headlessui/react';
import StatusBadge from '../../components/StatusBadge';
import config from "../../../config.json";
import * as XLSX from 'xlsx'; // <--- ADD THIS IMPORT

// --- Centralized Status Definitions (from your Mongoose Schema) ---
// const KYC_VERIFICATION_STATUS_OPTIONS = ["INITIATED", "REINITIATED", "UNDER_REVIEW", "APPROVED", "REJECTED", "REVIEW", "INFO_NEEDED"];
const AML_STATUS_OPTIONS = ['NOT_CHECKED', 'PENDING', 'CLEAR', 'HIT', 'ERROR', 'REJECTED', 'SKIPPED'];
const VIDEO_KYC_STATUS_OPTIONS = ['NOT_ATTEMPTED', 'INITIATED', 'PENDING_REVIEW', 'APPROVED', 'REJECTED', 'FAILED_ATTEMPT', 'EXPIRED', 'SKIPPED'];
const ADDRESS_VERIFICATION_STATUS_OPTIONS = ['NOT_ATTEMPTED', 'INITIATED', 'PENDING', 'VERIFIED', 'REJECTED', 'SKIPPED', 'ERROR'];
// const DOCUMENT_KYC_STATUS_OPTIONS = ['NOT_ATTEMPTED', 'SUBMITTED', 'PENDING_REVIEW', 'VERIFIED', 'REJECTED', 'INFO_NEEDED', 'EXPIRED', 'SKIPPED', 'ERROR'];
const BUSINESS_ADDRESS_VERIFICATION_STATUS_OPTIONS = ['INITIATED', 'PENDING', 'VERIFIED', 'REJECTED', 'SKIPPED']; // As per kyc.businessDetails.businessAddressVerification

// --- KycFilterSection Component ---
const KycFilterSection = ({ filters, setFilters, resetFilters }) => {
  const inputBaseClass = "block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm";
  const dateInputClass = `${inputBaseClass} px-2 py-1`;
  const textInputClass = `${inputBaseClass} px-3 py-1.5`;
  const selectInputClass = `${inputBaseClass} px-3 py-2`;

  const handleFilterChange = (event) => {
    const { name, value } = event.target;
    setFilters(prevFilters => ({
      ...prevFilters,
      [name]: value,
    }));
  };

  const activeFilterCount = useMemo(() => {
    return Object.values(filters).filter(value => value !== '' && value !== null && value !== undefined).length;
  }, [filters]);

  const handleReset = (event) => {
    event.stopPropagation();
    resetFilters();
  };

  // Define options for your dropdowns based on possible values in your data
  const kycStatusOptions = ['INITIATED', 'REINITIATED', 'UNDER_REVIEW', 'APPROVED', 'REJECTED', 'REVIEW', 'INFO_NEEDED'];
  const amlStatusOptions = ['NOT_CHECKED', 'PENDING', 'CLEAR', 'HIT', 'ERROR', 'REJECTED', 'SKIPPED'];
  const businessTypeOptions = ['SOLE_PROPRIETORSHIP', 'PARTNERSHIP', 'CORPORATION', 'OTHER']; // Example, adjust to your actual types
  const sectorOptions = ['FINTECH', 'HEALTHCARE', 'RETAIL', 'MANUFACTURING', 'OTHER']; // Example, adjust to your actual sectors

  return (
    <div className="mb-6">
      <Disclosure as="div" className="border border-gray-200 rounded-lg shadow-sm bg-white">
        {({ open }) => (
          <>
            <div className="flow-root">
              <Disclosure.Button className="flex w-full items-center justify-between px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus-visible:ring focus-visible:ring-indigo-500 focus-visible:ring-opacity-75">
                <span className="flex items-center">
                  <FunnelIcon className="mr-2 h-5 w-5 text-gray-400" aria-hidden="true" />
                  Filters
                  {activeFilterCount > 0 && (
                    <span className="ml-2 rounded-full bg-gray-200 px-2 py-0.5 text-xs font-medium text-gray-800">
                      {activeFilterCount}
                    </span>
                  )}
                </span>
                <span className="ml-6 flex items-center">
                  <ChevronUpIcon
                    className={`${open ? 'rotate-180 transform' : ''} h-5 w-5 text-gray-500 transition-transform duration-150 ease-in-out`}
                  />
                </span>
              </Disclosure.Button>
            </div>

            {open && <div className="border-t border-gray-200"></div>}

            <Disclosure.Panel className="px-4 py-5 sm:px-6 lg:px-8">
              <div className="mb-6 flex items-start justify-between gap-4">
                <div className="flex-1">
                  <label htmlFor="kycSearchTerm" className="sr-only">Search</label>
                  <input
                    type="text"
                    name="searchTerm"
                    id="kycSearchTerm"
                    value={filters.searchTerm}
                    onChange={handleFilterChange}
                    className={textInputClass}
                    placeholder="Search by Name, Email, or CR Number..."
                  />
                </div>
                <button
                  type="button"
                  onClick={handleReset}
                  className="inline-flex items-center justify-center rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                >
                  <XMarkIcon className="-ml-1 mr-1.5 h-4 w-4 text-gray-400" aria-hidden="true" />
                  Clear all
                </button>
              </div>

              <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
                {/* Submission Date Range */}
                <div className="sm:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Submission Date Range</label>
                  <div className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:items-center sm:space-x-2">
                    <input type="date" name="startDate" value={filters.startDate} onChange={handleFilterChange} className={dateInputClass + ' flex-1'} aria-label="Start Date" />
                    <span className="text-gray-500 text-center hidden sm:inline">to</span>
                    <input type="date" name="endDate" value={filters.endDate} onChange={handleFilterChange} className={dateInputClass + ' flex-1'} aria-label="End Date" />
                  </div>
                </div>

                {/* KYC Status Dropdown */}
                <div>
                  <label htmlFor="kycStatusFilter" className="block text-sm font-medium text-gray-700 mb-1">KYC Status</label>
                  <select id="kycStatusFilter" name="kycStatus" value={filters.kycStatus} onChange={handleFilterChange} className={selectInputClass}>
                    <option value="">All Statuses</option>
                    {kycStatusOptions.map(status => <option key={status} value={status}>{status.replace('_', ' ')}</option>)}
                  </select>
                </div>

                {/* AML Status Dropdown */}
                <div>
                  <label htmlFor="amlStatusFilter" className="block text-sm font-medium text-gray-700 mb-1">AML Status</label>
                  <select id="amlStatusFilter" name="amlStatus" value={filters.amlStatus} onChange={handleFilterChange} className={selectInputClass}>
                    <option value="">All Statuses</option>
                    {amlStatusOptions.map(status => <option key={status} value={status}>{status.replace('_', ' ')}</option>)}
                  </select>
                </div>

                {/* Business Type Dropdown */}
                <div>
                  <label htmlFor="businessTypeFilter" className="block text-sm font-medium text-gray-700 mb-1">Business Type</label>
                  <select id="businessTypeFilter" name="businessType" value={filters.businessType} onChange={handleFilterChange} className={selectInputClass}>
                    <option value="">All Types</option>
                    {businessTypeOptions.map(type => <option key={type} value={type}>{type.replace('_', ' ')}</option>)}
                  </select>
                </div>

                {/* Sector Dropdown */}
                <div>
                  <label htmlFor="sectorFilter" className="block text-sm font-medium text-gray-700 mb-1">Sector</label>
                  <select id="sectorFilter" name="sector" value={filters.sector} onChange={handleFilterChange} className={selectInputClass}>
                    <option value="">All Sectors</option>
                    {sectorOptions.map(sector => <option key={sector} value={sector}>{sector.replace('_', ' ')}</option>)}
                  </select>
                </div>

                {/* Is Active Toggle (Example for boolean filter) */}
                <div>
                  <label htmlFor="isActiveFilter" className="block text-sm font-medium text-gray-700 mb-1">Is Active</label>
                  <select id="isActiveFilter" name="isActive" value={filters.isActive} onChange={handleFilterChange} className={selectInputClass}>
                    <option value="">All</option>
                    <option value="true">Yes</option>
                    <option value="false">No</option>
                  </select>
                </div>

              </div>
            </Disclosure.Panel>
          </>
        )}
      </Disclosure>
    </div>
  );
};

// --- START: Helper Function to Check Shareholder Auto-Approval Conditions ---
const checkShareholderAllComponentsApproved = (shareholder) => {
  if (!shareholder) return false;

  // Define the "good" statuses for each component
  const passportStatus = getNested(shareholder, 'passport.verificationStatus');
  const qidStatus = getNested(shareholder, 'qid.verificationStatus');
  const proofOfAddressStatus = getNested(shareholder, 'proofOfAddress.verificationStatus');
  const amlStatus = getNested(shareholder, 'amlStatus');
  const videoKycStatus = getNested(shareholder, 'videoKyc.status');
  const addressVerificationStatus = getNested(shareholder, 'addressVerification.status');

  // Check if all conditions are met
  const documentsVerified = passportStatus === 'VERIFIED' &&
    qidStatus === 'VERIFIED' &&
    proofOfAddressStatus === 'VERIFIED';

  const amlClear = amlStatus === 'CLEAR';
  const videoKycApproved = videoKycStatus === 'APPROVED';
  const addressVerified = addressVerificationStatus === 'VERIFIED';

  const allConditionsMet = documentsVerified &&
    amlClear &&
    videoKycApproved &&
    addressVerified;

  return allConditionsMet;
};
// --- END: Helper Function ---

// --- START: NEW Helper Function to Check All Required Statuses ---
const checkAllRequiredStatusesPass = (user) => {
  const failures = [];
  const shareholders = getNested(user, 'shareholders', []) || [];

  // Define the specific statuses and their required passing values for approval
  // Structure: { path: string, expected: string[], label: string, entity: 'User' | 'Shareholder' }
  const statusChecks = [
    // ---- User Statuses to Check ----
    // { path: 'kyc.verificationStatus', expected: ['APPROVED'], label: 'Overall Applicant KYC Status', entity: 'User' },
    { path: 'kyc.amlStatus', expected: ['CLEAR'], label: 'Applicant AML Status', entity: 'User' },
    // { path: 'kyc.videoKyc.status', expected: ['APPROVED'], label: 'Applicant Video KYC Status', entity: 'User' },
    { path: 'kyc.businessDetails.businessAddressVerification.status', expected: ['VERIFIED'], label: 'Business Address Verification Status', entity: 'User' },

    // ---- Shareholder Statuses to Check (per shareholder) ----
    // IMPORTANT: Add other relevant shareholder statuses here if needed based on schema/requirements
    { path: 'kycVerificationStatus', expected: ['APPROVED'], label: 'Overall KYC Status', entity: 'Shareholder' },
    { path: 'amlStatus', expected: ['CLEAR'], label: 'AML Status', entity: 'Shareholder' },
    { path: 'videoKyc.status', expected: ['APPROVED'], label: 'Video KYC Status', entity: 'Shareholder' },
    { path: 'addressVerification.status', expected: ['VERIFIED'], label: 'Address Verification Status', entity: 'Shareholder' },
    // EXCLUDED as per request: 'documentKycStatus'
  ];

  // --- Check User Statuses ---
  statusChecks.filter(c => c.entity === 'User').forEach(check => {
    const currentStatus = getNested(user, check.path);
    // Check if the current status is NOT in the array of expected statuses
    if (!check.expected.includes(currentStatus)) {
      failures.push({
        entityDescription: 'Applicant', // Use 'Applicant' for user-level checks
        label: check.label,
        path: check.path, // For debugging if needed
        current: currentStatus || 'Not Set/Found', // Handle undefined/null
        expected: check.expected.join(' or ') // Show expected value(s)
      });
    }
  });

  // --- Check Shareholder Statuses ---
  shareholders.forEach((sh, index) => {
    const shareholderName = `${getNested(sh, 'firstName', '')} ${getNested(sh, 'lastName', '')}`.trim();
    const shareholderDesc = `Shareholder ${index + 1}${shareholderName ? ` (${shareholderName})` : ''}`;

    statusChecks.filter(c => c.entity === 'Shareholder').forEach(check => {
      const currentStatus = getNested(sh, check.path);
      // Check if the current status is NOT in the array of expected statuses
      if (!check.expected.includes(currentStatus)) {
        failures.push({
          entityDescription: shareholderDesc,
          label: check.label, // e.g., "AML Status"
          path: `shareholders[${index}].${check.path}`, // For debugging
          current: currentStatus || 'Not Set/Found',
          expected: check.expected.join(' or ')
        });
      }
    });
  });

  // Return whether all checks passed and the list of failures
  return {
    passes: failures.length === 0,
    failures: failures // Array of objects detailing what failed
  };
};
// --- END: NEW Helper Function to Check All Required Statuses ---

// --- StatusDisplayControl Component (can be defined in the same file or imported) ---
const StatusDisplayControl = ({
  label,
  currentStatus,
  statusOptions,
  onStatusChange, // This function will now be simpler: (newValue) => void
  isLoading,
  error,
  disabled = false, // General disable prop
}) => {
  const [selectedStatus, setSelectedStatus] = useState(currentStatus);

  useEffect(() => {
    setSelectedStatus(currentStatus);
  }, [currentStatus]);

  const handleChange = (e) => {
    const newStatus = e.target.value;
    setSelectedStatus(newStatus); // Optimistic update for dropdown display
    onStatusChange(newStatus); // Call the handler passed from parent
  };

  // Determine if the component should be disabled
  const isDisabled = isLoading || disabled;

  return (
    <div className="flex flex-col sm:flex-row p-1.5">
      <dt className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-1 sm:mb-0">
        {label}
      </dt>
      <dd className="mt-0.5 sm:mt-0 flex items-center space-x-2">
        <StatusBadge status={currentStatus || (statusOptions.includes('NOT_ATTEMPTED') ? 'NOT_ATTEMPTED' : statusOptions[0])} />
        <select
          value={selectedStatus || (statusOptions.includes('NOT_ATTEMPTED') ? 'NOT_ATTEMPTED' : statusOptions[0])}
          onChange={handleChange}
          disabled={isDisabled}
          className={`text-xs rounded-md border-gray-300 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 py-1 pl-2 pr-7 ${isDisabled ? 'bg-gray-100 cursor-not-allowed opacity-70' : 'bg-white hover:border-gray-400'
            }`}
        >
          {statusOptions.map(option => (
            <option key={option} value={option}>
              {option.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
            </option>
          ))}
        </select>
        {isLoading && (
          <div className="h-4 w-4 animate-spin rounded-full border-2 border-t-indigo-600 border-r-indigo-600 border-b-indigo-600 border-l-gray-300"></div>
        )}
        {error && <p className="text-xs text-red-500">{error}</p>}
      </dd>
    </div>
  );
};

// Helper function to safely get nested properties
const getNested = (obj, path, defaultValue = undefined) => {
  console.log(obj);
  // Ensure path is a string before splitting
  if (typeof path !== 'string' || path === '') {
    console.error("getNested: Invalid path provided.", path);
    return defaultValue;
  }
  try {
    // Attempt to reduce the path
    const value = path.split('.').reduce((o, k) => {
      // Ensure o is a valid object/array before trying to access key k
      // If o is null, undefined, or not an object type, stop reduction safely
      return (o && typeof o === 'object' && k in o) ? o[k] : undefined;
    }, obj);

    // Return the defaultValue if the final value is null or undefined
    // Otherwise, return the found value.
    if (path == "commercialRegistration.shuftiCallbackData.verification_result.aml_for_businesses") {
      if (value === 0) {
        return "REJECTED";
      } else {
        return "VERIFIED";
      }
    }
    return (value === undefined || value === null) ? defaultValue : value;

  } catch (e) {
    // Catch potential errors during split or reduce (though checks above reduce likelihood)
    console.error("Error in getNested:", e, "Path:", path, "Object:", obj);
    return defaultValue;
  }
};

// --- START: Add Helper Function to Check Document Verification ---
const checkAllRequiredDocumentsVerified = (user) => {
  if (!user) return false; // Cannot check if no user data

  // Define paths to check. Combine static paths and dynamically generated ones.
  // Use the same field definitions as in the render logic for consistency.
  const businessDocFields = [
    { key: 'commercialRegistration', label: 'Commercial Registration', isRequired: true },
    { key: 'tradeLicense', label: 'Trade License', isRequired: true },
    { key: 'taxCard', label: 'Tax Card', isRequired: true },
    { key: 'establishmentCard', label: 'Establishment Card', isRequired: true },
    { key: 'memorandumOfAssociation', label: 'Memorandum of Association', isRequired: true },
    { key: 'articleOfAssociation', label: 'Article of Association', isRequired: true },
    { key: 'commercialCreditReport', label: 'Commercial Credit Report', isRequired: false },
    // Add other optional business docs if they *become* required under certain conditions
  ];
  const financialDocFields = [
    { key: 'bankStatement', label: 'Bank Statement (Last 6 months)', isRequired: true },
    { key: 'auditedFinancialReport', label: 'Audited Financial Report (12mo)', isRequired: true },
    { key: 'cashFlowLedger', label: 'Cash Flow / Ledger', isRequired: true },
  ];

  // List of all document paths to check
  const documentPathsToCheck = [];

  // Add required business documents
  businessDocFields.forEach(field => {
    if (field.isRequired) {
      documentPathsToCheck.push({ path: field.key, isRequired: true, label: field.label });
    }
  });

  // Add required financial documents
  financialDocFields.forEach(field => {
    if (field.isRequired) {
      documentPathsToCheck.push({ path: field.key, isRequired: true, label: field.label });
    }
  });

  // Add KYC Personal documents (Assuming these are always required)
  documentPathsToCheck.push({ path: 'kyc.qatariId', isRequired: true, label: 'Qatari ID (KYC)' });
  documentPathsToCheck.push({ path: 'kyc.passport', isRequired: true, label: 'Passport (KYC)' });
  documentPathsToCheck.push({ path: 'kyc.utilityBill', isRequired: true, label: 'Utility Bill (KYC)' });

  // Add KYC Employment/Income documents (Assuming these are always required)
  documentPathsToCheck.push({ path: 'kyc.employmentDetails.employmentLetter', isRequired: true, label: 'Employment Letter' });
  documentPathsToCheck.push({ path: 'kyc.incomeDetails.proofOfIncome', isRequired: true, label: 'Proof of Income' });


  // Add Shareholder documents (Required if shareholder exists)
  (getNested(user, 'shareholders', []) || []).forEach((sh, index) => {
    // Assuming passport, QID, and proofOfAddress are required per shareholder
    documentPathsToCheck.push({ path: `shareholders.${index}.passport`, isRequired: true, label: `Shareholder ${index + 1} Passport` });
    documentPathsToCheck.push({ path: `shareholders.${index}.qid`, isRequired: true, label: `Shareholder ${index + 1} QID` });
    documentPathsToCheck.push({ path: `shareholders.${index}.proofOfAddress`, isRequired: true, label: `Shareholder ${index + 1} Proof of Address` });
  });

  // Add Director documents (Required if director exists)
  (getNested(user, 'kyc.directors', []) || []).forEach((dir, index) => {
    // Assuming idDocument is required per director
    documentPathsToCheck.push({ path: `kyc.directors.${index}.idDocument`, isRequired: true, label: `Director ${index + 1} ID Document` });
  });

  // Add Authorized Signatory documents (Required if signatory exists)
  (getNested(user, 'authorizedSignatories', []) || []).forEach((sig, index) => {
    // Assuming idDocument is required per signatory
    documentPathsToCheck.push({ path: `authorizedSignatories.${index}.idDocument`, isRequired: true, label: `Signatory ${index + 1} ID Document` });
  });

  // Add Beneficial Owner documents (Required if owner exists)
  (getNested(user, 'beneficialOwners', []) || []).forEach((owner, index) => {
    // Assuming idDocument is required per owner
    documentPathsToCheck.push({ path: `beneficialOwners.${index}.idDocument`, isRequired: true, label: `Owner ${index + 1} ID Document` });
  });

  // Add Buyer documents (Required if buyer exists AND companyDocument exists? Check requirement)
  // Assuming buyer company docs are NOT strictly required for overall KYC approval unless specified
  // (getNested(user, 'kyc.buyers', []) || []).forEach((buyer, index) => {
  //     documentPathsToCheck.push({ path: `kyc.buyers.${index}.companyDocument`, isRequired: false }); // Example: Assuming NOT required for overall KYC
  // });


  // Now, iterate through the combined list and check status
  for (const docInfo of documentPathsToCheck) {
    const docData = getNested(user, docInfo.path, null);
    const docExists = docData && (getNested(docData, 'filePath', '') || getNested(docData, 'signedUrl', ''));

    // Only check status if the document is required AND it has actually been uploaded
    if (docInfo.isRequired && docExists) {
      const status = getNested(docData, 'verificationStatus', 'PENDING');
      if (status !== 'VERIFIED') {
        console.warn(`KYC Approval Check Failed: Required document "${docInfo.label}" (Path: ${docInfo.path}) is not verified. Status: ${status}`);
        return false; // Found a required, existing document that is not verified
      }
    }
    // Optional: Add a check for required documents that *haven't* been uploaded yet
    // else if (docInfo.isRequired && !docExists) {
    //   console.warn(`KYC Approval Check Failed: Required document "${docInfo.label}" (Path: ${docInfo.path}) is missing.`);
    //   return false; // Found a required document that is missing
    // }
  }

  // If the loop completes without returning false, all required & existing documents are verified
  return true;
};
// --- END: Add Helper Function to Check Document Verification ---

// Helper function to format dates - Robust Version
const formatDate = (dateString) => {
  // Check for various invalid/missing inputs
  if (!dateString || dateString === 'N/A' || typeof dateString !== 'string') {
    return 'N/A';
  }
  try {
    const date = new Date(dateString);
    // Check if the date object is valid (getTime returns NaN for invalid dates)
    // Also check for potentially invalid years (like Unix epoch default)
    if (isNaN(date.getTime()) || date.getFullYear() <= 1970) {
      return 'N/A';
    }
    // Format the valid date
    return date.toLocaleDateString('en-GB', {
      day: '2-digit',
      month: 'short',
      year: 'numeric'
    });
  } catch (e) {
    console.error("Error formatting date:", dateString, e);
    // Return a specific string for errors during processing
    return 'Invalid Date';
  }
};

const RenderDocumentDisplay = ({ userId, documentData, documentFieldPath, label, isRequired = false, onUpdateSuccess }) => {
  const initialNotes = getNested(documentData, 'verificationNotes', '');
  const initialStatus = getNested(documentData, 'verificationStatus', 'PENDING');
  // --- NEW: Get initial request status ---
  const initialIsRequested = getNested(documentData, 'isRequested', false);

  const [docNotes, setDocNotes] = useState(initialNotes);
  const [currentStatus, setCurrentStatus] = useState(initialStatus);
  const [isUpdating, setIsUpdating] = useState(false);
  const [updateError, setUpdateError] = useState(null);
  // --- NEW: State for request status and loading ---
  const [isRequested, setIsRequested] = useState(initialIsRequested);
  const [isRequestingDoc, setIsRequestingDoc] = useState(false);
  const [requestDocError, setRequestDocError] = useState(null);


  useEffect(() => {
    setCurrentStatus(getNested(documentData, 'verificationStatus', 'PENDING'));
    setDocNotes(getNested(documentData, 'verificationNotes', ''));
    // --- NEW: Update local state for isRequested ---
    setIsRequested(getNested(documentData, 'isRequested', false));
    setUpdateError(null);
    setIsUpdating(false);
    // Reset request specific states
    setIsRequestingDoc(false);
    setRequestDocError(null);
  }, [documentData]);

  const handleDocUpdate = async (newStatus) => {
    setIsUpdating(true);
    setUpdateError(null);

    if (!userId || !documentFieldPath) {
      console.error("Missing userId or documentFieldPath for update.");
      setUpdateError("Internal configuration error.");
      setIsUpdating(false);
      return;
    }

    try {
      const response = await fetch(`${config.apiUrl}/ops/invoiceFinancing/updateDocumentStatus`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: userId,
          documentFieldPath: documentFieldPath,
          verificationStatus: newStatus,
          verificationNotes: docNotes,
        }),
      });

      if (!response.ok) {
        let errorMsg = `Failed to update document status (${response.status})`;
        try {
          const errorResult = await response.json();
          errorMsg = errorResult.message || errorMsg;
        } catch (parseError) {
          console.log(parseError);
        }
        throw new Error(errorMsg);
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.message || 'Update failed according to server.');
      }

      setCurrentStatus(newStatus);

      if (onUpdateSuccess) {
        const updatedDocData = {
          ...(documentData || {}),
          verificationStatus: newStatus,
          verificationNotes: docNotes,
          verifiedOrRejectedOn: new Date().toISOString(),
        };
        onUpdateSuccess(documentFieldPath, updatedDocData);
      }

    } catch (error) {
      console.error(`Error updating document ${documentFieldPath}:`, error);
      setUpdateError(error.message || 'An unexpected error occurred.');
    } finally {
      setIsUpdating(false);
    }
  };

  // --- NEW: Handle Document Request ---
  const handleRequestDocument = async () => {
    setIsRequestingDoc(true);
    setRequestDocError(null);

    if (!userId || !documentFieldPath) {
      console.error("Missing userId or documentFieldPath for request.");
      setRequestDocError("Internal configuration error.");
      setIsRequestingDoc(false);
      return;
    }

    try {
      const response = await fetch(`${config.apiUrl}/ops/invoiceFinancing/requestDocumentForUpload`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: userId,
          documentFieldPath: documentFieldPath,
        }),
      });

      if (!response.ok) {
        let errorMsg = `Failed to request document (${response.status})`;
        try {
          const errorResult = await response.json();
          errorMsg = errorResult.message || errorMsg;
        } catch (parseError) {
          console.log(parseError);
        }
        throw new Error(errorMsg);
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.message || 'Document request failed according to server.');
      }

      // --- Update local state and notify parent ---
      setIsRequested(true); // Mark as requested immediately
      if (onUpdateSuccess) {
        const updatedDocData = {
          ...(documentData || {}),
          isRequested: true,
          requestedOn: new Date().toISOString(), // Record approximate request time
        };
        onUpdateSuccess(documentFieldPath, updatedDocData);
      }
      alert(`Request for "${label}" document sent successfully!`); // User feedback

    } catch (error) {
      console.error(`Error requesting document ${documentFieldPath}:`, error);
      setRequestDocError(error.message || 'An unexpected error occurred.');
    } finally {
      setIsRequestingDoc(false);
    }
  };
  // --- END NEW: Handle Document Request ---

  const filePathString = getNested(documentData, 'filePath', '');
  const fileName = filePathString.split('/').pop() || 'N/A';

  const displayFileName = fileName.length > 20
    ? fileName.substring(0, 16) + '...' + fileName.substring(fileName.lastIndexOf('.'))
    : fileName;

  const viewUrl = getNested(documentData, 'signedUrl', '');
  const uploadedOn = getNested(documentData, 'uploadedOn');

  const documentIsUploaded = typeof viewUrl === 'string' && viewUrl.length > 0;

  const statusConfig = {
    VERIFIED: {
      bgColor: 'bg-emerald-50',
      textColor: 'text-emerald-700',
      borderColor: 'border-emerald-200',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
        </svg>
      )
    },
    REJECTED: {
      bgColor: 'bg-rose-50',
      textColor: 'text-rose-700',
      borderColor: 'border-rose-200',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
        </svg>
      )
    },
    PENDING: {
      bgColor: 'bg-amber-50',
      textColor: 'text-amber-700',
      borderColor: 'border-amber-200',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      )
    },
    SUBMITTED: {
      bgColor: 'bg-blue-50',
      textColor: 'text-blue-700',
      borderColor: 'border-blue-200',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      )
    },
    // --- NEW: Status for Requested ---
    REQUESTED: {
      bgColor: 'bg-gray-100',
      textColor: 'text-gray-600',
      borderColor: 'border-gray-300',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      )
    }
  };

  const currentStatusConfig = statusConfig[currentStatus] || statusConfig.PENDING;

  const StatusBadge = ({ status }) => {
    const config = statusConfig[status] || statusConfig.PENDING;
    return (
      <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${config.bgColor} ${config.textColor} ${config.borderColor} border`}>
        {config.icon}
        <span className="ml-1">{status}</span>
      </span>
    );
  };

  return (
    <div className={`flex flex-col h-full border border-gray-200 rounded-lg bg-white shadow-sm transition-all duration-200 hover:shadow-md overflow-hidden`}>
      {/* Header Section */}
      <div className="flex justify-between items-center px-4 py-3 bg-gray-50 border-b border-gray-200">
        <h4 className="text-sm font-medium text-gray-800 flex items-center truncate" title={label}>
          {label || 'Document'}
          {isRequired && <span className="text-rose-500 ml-1">*</span>}
        </h4>
        {documentIsUploaded ? (
          <StatusBadge status={currentStatus} />
        ) : (
          // --- NEW: Display Requested status if not uploaded ---
          isRequested && <StatusBadge status="REQUESTED" />
        )}
      </div>

      {/* Content Section */}
      <div className="flex-grow p-4">
        {!documentIsUploaded ? (
          <div className="flex flex-col items-center justify-center h-full py-6 text-gray-500">
            <div className="p-3 rounded-full bg-gray-100 mb-2">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 13h6m-3-3v6m-9 1V7a2 2 0 012-2h6l2 2h6a2 2 0 012 2v8a2 2 0 01-2 2H5a2 2 0 01-2-2z" />
              </svg>
            </div>
            <p className="text-sm text-center">Document not uploaded</p>
            {/* --- NEW: Request Document Button --- */}
            {requestDocError && (
              <p className="text-xs text-red-500 mt-2">{requestDocError}</p>
            )}
            <button
              onClick={handleRequestDocument}
              disabled={isRequestingDoc || isRequested} // Disable if already requested or currently requesting
              className={`mt-4 px-4 py-2 text-sm font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 transition duration-150 ease-in-out
                                ${isRequestingDoc || isRequested
                  ? 'bg-gray-200 text-gray-600 cursor-not-allowed'
                  : 'bg-indigo-600 text-white hover:bg-indigo-700 focus:ring-indigo-500'
                }`}
            >
              {isRequestingDoc ? (
                <span className="flex items-center">
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Requesting...
                </span>
              ) : (
                isRequested ? 'Requested' : 'Request Document'
              )}
            </button>
            {/* --- END NEW: Request Document Button --- */}
          </div>
        ) : (
          <div className="flex flex-col h-full">
            {/* Document Info */}
            <div className="mb-3">
              <div className="flex items-start mb-2">
                <div className={`flex-shrink-0 p-2 rounded-md mr-2 ${currentStatusConfig.bgColor} ${currentStatusConfig.textColor}`}>
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                  </svg>
                </div>
                <div className="min-w-0">
                  <p className="font-medium text-sm text-gray-700 truncate" title={fileName}>
                    {displayFileName}
                  </p>
                  <p className="text-xs text-gray-500">
                    Uploaded: {formatDate(uploadedOn)}
                  </p>
                </div>
              </div>

              {viewUrl && (
                <a
                  href={viewUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center justify-center w-full px-3 py-1.5 text-xs font-medium text-indigo-600 bg-indigo-50 hover:bg-indigo-100 rounded-md transition-colors duration-150"
                  title={`View ${fileName}`}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                  View Document
                </a>
              )}
            </div>

            {/* Textarea and buttons */}
            <div className="flex-grow flex flex-col">
              <div className="relative flex-grow mb-2">
                <textarea
                  value={docNotes}
                  onChange={(e) => setDocNotes(e.target.value)}
                  rows={2}
                  className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-xs py-1.5 px-2 text-gray-700 placeholder-gray-400 resize-none transition duration-150 ease-in-out"
                  placeholder="Add verification notes..."
                  disabled={isUpdating}
                />
              </div>

              {updateError && (
                <div className="w-full bg-rose-50 p-1.5 rounded-md mb-2">
                  <p className="text-xs text-rose-600 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    {updateError}
                  </p>
                </div>
              )}

              <div className="flex items-center justify-between gap-2 mt-auto">
                <button
                  onClick={() => handleDocUpdate('REJECTED')}
                  disabled={isUpdating || currentStatus === 'REJECTED'}
                  className={`flex-1 px-3 py-1.5 text-xs font-medium rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-rose-500 transition duration-150 ease-in-out ${isUpdating || currentStatus === 'REJECTED'
                    ? 'bg-rose-100 text-rose-600 cursor-not-allowed opacity-70'
                    : 'bg-rose-600 text-white hover:bg-rose-700'
                    }`}
                >
                  {isUpdating && currentStatus !== 'REJECTED' ? (
                    <span className="flex items-center justify-center">
                      <svg className="animate-spin -ml-0.5 mr-1.5 h-3 w-3 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Rejecting
                    </span>
                  ) : 'Reject'}
                </button>

                <button
                  onClick={() => handleDocUpdate('VERIFIED')}
                  disabled={isUpdating || currentStatus === 'VERIFIED'}
                  className={`flex-1 px-3 py-1.5 text-xs font-medium rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-emerald-500 transition duration-150 ease-in-out ${isUpdating || currentStatus === 'VERIFIED'
                    ? 'bg-emerald-100 text-emerald-600 cursor-not-allowed opacity-70'
                    : 'bg-emerald-600 text-white hover:bg-emerald-700'
                    }`}
                >
                  {isUpdating && currentStatus !== 'VERIFIED' ? (
                    <span className="flex items-center justify-center">
                      <svg className="animate-spin -ml-0.5 mr-1.5 h-3 w-3 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Verifying
                    </span>
                  ) : 'Verify'}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default function KycApprovalPage() {
  const [isUpdating, setIsUpdating] = useState(false);
  const [users, setUsers] = useState(null);
  const [selectedUser, setSelectedUser] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [overallVerificationStatus, setOverallVerificationStatus] = useState("INITIATED"); // For the overall status
  const [overallVerificationNotes, setOverallVerificationNotes] = useState(""); // For the overall notes
  const [loading, setLoading] = useState(true);
  const [fetchError, setFetchError] = useState(null);
  const [activeTab, setActiveTab] = useState('businessDocs'); // Default to new first tab
  const [noUsersFound, setNoUsersFound] = useState(false);
  const [isGeneratingPdf, setIsGeneratingPdf] = useState(false);
  // Add these lines near other useState hooks inside KycApprovalPage
  const [updatingShareholderIndex, setUpdatingShareholderIndex] = useState(null); // Track which shareholder is updating
  const [shareholderUpdateError, setShareholderUpdateError] = useState(null);
  const [specificStatusUpdate, setSpecificStatusUpdate] = useState({
    loadingPath: null, // e.g., "shareholders.0.amlStatus" or "kyc.videoKyc.status"
    errorPath: null,
    errorMessage: '',
  });
  // Inside the KycApprovalPage component
  const [isInitiatingKyb, setIsInitiatingKyb] = useState(false);
  const [isExportingExcel, setIsExportingExcel] = useState(false);
  const [emailSendStatus, setEmailSendStatus] = useState({
    loadingId: null, // Will hold a unique ID for the item being processed
    error: null,
  });
  const [kycFilters, setKycFilters] = useState({
    searchTerm: '',
    startDate: '', // Submission Date Start
    endDate: '',   // Submission Date End
    kycStatus: '',
    amlStatus: '',
    businessType: '',
    sector: '',
    isActive: '', // Boolean filter
  });

  const resetKycFilters = useCallback(() => {
    setKycFilters({
      searchTerm: '',
      startDate: '',
      endDate: '',
      kycStatus: '',
      amlStatus: '',
      businessType: '',
      sector: '',
      isActive: '',
    });
  }, []);

  // const createUserKycPayload = (path, value) => {
  //   const keys = path.split('.');
  //   let payload = {};
  //   let current = payload;
  //   for (let i = 0; i < keys.length - 1; i++) {
  //     current[keys[i]] = {};
  //     current = current[keys[i]];
  //   }
  //   current[keys[keys.length - 1]] = value;
  //   return payload;
  // };

  // Add this helper function somewhere accessible, perhaps above KycApprovalPage component

  /**
   * Creates a new object with an updated value at a nested path.
   * @param {object} obj The original object.
   * @param {string} path Dot-notation path (e.g., "videoKyc.status").
   * @param {*} value The new value to set.
   * @returns {object} A new object with the value updated.
   */

  // Add this new function inside the KycApprovalPage component
  const handleInitiateKybVerification = async () => {
    if (!selectedUser) return;

    setIsInitiatingKyb(true);

    try {
      const response = await fetch(`${config.apiUrl}/ops/invoiceFinancing/initiateShuftiVerification`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: selectedUser._id,
          documentType: 'commercialRegistration',
        }),
      });

      const result = await response.json();

      if (!response.ok || !result.success) {
        throw new Error(result.error || 'Failed to initiate KYB verification.');
      }

      alert('KYB/AML verification successfully initiated. The status will update shortly via callback.');

      // Optimistically update the local state to reflect the change
      handleDocumentUpdateSuccess('commercialRegistration', {
        ...(getNested(selectedUser, 'commercialRegistration') || {}),
        verificationStatus: 'PENDING', // Update status to PENDING
        verificationNotes: `KYB & AML verification initiated manually. Ref: ${result.shuftiReference}`,
        shuftiReferenceId: result.shuftiReference,
      });

    } catch (error) {
      console.error("Error initiating KYB verification:", error);
      alert(`Error: ${error.message}`);
    } finally {
      setIsInitiatingKyb(false);
    }
  };

  const handleSendShareholderKycEmail = async (shareholderId) => {
    if (!selectedUser) return;

    setEmailSendStatus({ loadingId: shareholderId, error: null });

    try {
      const response = await fetch(`${config.apiUrl}/ops/invoiceFinancing/initiateShareholderKyc`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: selectedUser._id,
          shareholderId: shareholderId,
        }),
      });

      const result = await response.json();

      if (!response.ok || !result.success) {
        throw new Error(result.error || 'Failed to send KYC email.');
      }

      // Update local state to mark the email as sent
      setSelectedUser(currentUser =>
        produce(currentUser, draft => {
          const shareholder = draft.shareholders.find(sh => sh._id === shareholderId);
          if (shareholder) {
            shareholder.kycEmailSent = true;
          }
        })
      );

      alert(result.message || 'Shareholder KYC email sent successfully!');
      setEmailSendStatus({ loadingId: null, error: null });

    } catch (error) {
      console.error("Error sending shareholder KYC email:", error);
      setEmailSendStatus({ loadingId: shareholderId, error: error.message });
      alert(`Error: ${error.message}`);
      // Optional: clear error after a delay
      setTimeout(() => setEmailSendStatus({ loadingId: null, error: null }), 5000);
    }
  };

  const handleSendBuyerInvitation = async (buyerEmail, buyerIndex) => {
    if (!selectedUser || !selectedUser.email || !buyerEmail) return;

    const loadingId = `buyer-${buyerIndex}`; // Unique ID for loading state
    setEmailSendStatus({ loadingId: loadingId, error: null });

    try {
      const response = await fetch(`${config.apiUrl}/ops/invoiceFinancing/send-buyer-invitation`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          msmeEmail: selectedUser.email,
          buyerEmail: buyerEmail,
        }),
      });

      const result = await response.json();

      if (!response.ok || !result.success) {
        throw new Error(result.message || 'Failed to send buyer invitation.');
      }

      // Update local state to mark the email as sent
      setSelectedUser(currentUser =>
        produce(currentUser, draft => {
          if (draft.kyc && draft.kyc.buyers && draft.kyc.buyers[buyerIndex]) {
            draft.kyc.buyers[buyerIndex].buyerEmailSent = true;
          }
        })
      );

      alert(result.message || 'Buyer invitation sent successfully!');
      setEmailSendStatus({ loadingId: null, error: null });

    } catch (error) {
      console.error("Error sending buyer invitation:", error);
      setEmailSendStatus({ loadingId: loadingId, error: error.message });
      alert(`Error: ${error.message}`);
      // Optional: clear error after a delay
      setTimeout(() => setEmailSendStatus({ loadingId: null, error: null }), 5000);
    }
  };

  const setNestedValueImmutable = (obj, path, value) => {
    // Use Immer to produce the next state immutably
    return produce(obj, draft => {
      const keys = path.split('.');
      let current = draft;
      for (let i = 0; i < keys.length - 1; i++) {
        const key = keys[i];
        // Ensure path exists in draft, create if necessary
        // Handle cases where intermediate path might be null or not an object
        if (current[key] === undefined || current[key] === null || typeof current[key] !== 'object') {
          // Create an empty object if the path doesn't exist or isn't an object
          current[key] = {};
        }
        current = current[key];
      }
      // Set the final value at the target key
      current[keys[keys.length - 1]] = value;
    });
  };

  const handleGenericStatusUpdate = async (entityType, fieldPath, newValue, entityIdOrIndex) => {
    // --- Existing setup code remains the same ---
    if (!selectedUser || !selectedUser._id) {
      console.error("Cannot perform update: selectedUser or selectedUser._id is missing.");
      setSpecificStatusUpdate({ loadingPath: null, errorPath: null, errorMessage: "Selected user data is missing." });
      return;
    }
    const userId = selectedUser._id;
    const loadingKey = entityType === 'user'
      ? `user.${fieldPath}` // Note: fieldPath is like "amlStatus" or "videoKyc.status"
      : `shareholders.${entityIdOrIndex}.${fieldPath}`;

    setSpecificStatusUpdate({ loadingPath: loadingKey, errorPath: null, errorMessage: '' });

    let url = '';
    let body = {};

    try {
      if (entityType === 'user') {
        url = `${config.apiUrl}/ops/invoiceFinancing/updateKyc`;

        // --- START: MODIFICATION FOR FRONTEND FIX ---
        // Get the current state of the entire kyc object.
        // Ensure selectedUser.kyc exists. Default to empty object if needed,
        // though ideally it should always exist for a user being reviewed.
        const currentKycData = selectedUser.kyc || {};

        // Create the *next* state of the kyc object by applying the specific change
        // using the immutable helper function. `fieldPath` is relative to `kyc`.
        const updatedKycData = setNestedValueImmutable(currentKycData, fieldPath, newValue);

        // Include potentially related timestamp updates directly in the payload
        // Note: This duplicates the Immer logic below, but ensures the payload sent is complete
        const nowISO = new Date().toISOString();
        let finalKycPayload = updatedKycData;
        if (fieldPath === 'videoKyc.status') {
          finalKycPayload = setNestedValueImmutable(finalKycPayload, 'videoKyc.completedOn', nowISO);
        } else if (fieldPath === 'businessDetails.businessAddressVerification.status') {
          finalKycPayload = setNestedValueImmutable(finalKycPayload, 'businessDetails.businessAddressVerification.verifiedOn', nowISO);
        } else if (fieldPath === 'amlStatus') {
          finalKycPayload = setNestedValueImmutable(finalKycPayload, 'amlLastChecked', nowISO);
        }
        // Add timestamp for overall verification update if needed (though this is handled in handleUpdateOverallKyc)
        // if (fieldPath === 'verificationStatus') {
        //    finalKycPayload = setNestedValueImmutable(finalKycPayload, 'verifiedOn', nowISO);
        // }


        // Send the *entire, updated* kyc object as the payload for the 'kyc' field.
        // The backend's `$set: { kyc: receivedKycObject }` will now replace the DB kyc
        // object with the full, correctly updated one from the frontend.
        body = { id: userId, kyc: finalKycPayload };

        console.log("[Frontend Fix] Sending full updated kyc payload:", JSON.stringify(finalKycPayload, null, 2));
        // --- END: MODIFICATION FOR FRONTEND FIX ---

      } else if (entityType === 'shareholder') {
        // --- Shareholder logic remains unchanged ---
        url = `${config.apiUrl}/ops/invoiceFinancing/shareholders/updateTextData`;
        const shareholderIndex = entityIdOrIndex;

        if (shareholderIndex === undefined || shareholderIndex === null || !selectedUser.shareholders || shareholderIndex < 0 || shareholderIndex >= selectedUser.shareholders.length) {
          throw new Error(`Invalid shareholder index (${shareholderIndex}) provided for update.`);
        }

        const shareholdersFullPayload = selectedUser.shareholders.map((sh, index) => {
          const currentShareholderData = sh.toObject ? sh.toObject() : sh;
          if (index === shareholderIndex) {
            const updatedFieldsOnly = {};
            const pathParts = fieldPath.split('.');
            let targetInUpdatedFields = updatedFieldsOnly;
            for (let i = 0; i < pathParts.length - 1; i++) {
              targetInUpdatedFields[pathParts[i]] = targetInUpdatedFields[pathParts[i]] || {};
              targetInUpdatedFields = targetInUpdatedFields[pathParts[i]];
            }
            targetInUpdatedFields[pathParts[pathParts.length - 1]] = newValue;

            const fullShareholderPayload = {
              _id: currentShareholderData._id,
              firstName: currentShareholderData.firstName,
              middleName: currentShareholderData.middleName,
              lastName: currentShareholderData.lastName,
              kycVerificationStatus: currentShareholderData.kycVerificationStatus,
              verificationNotes: currentShareholderData.verificationNotes,
              email: currentShareholderData.email,
              address: currentShareholderData.address,
              shuftiReferenceId: currentShareholderData.shuftiReferenceId,
              amlStatus: currentShareholderData.amlStatus,
              amlNotes: currentShareholderData.amlNotes,
              amlLastChecked: currentShareholderData.amlLastChecked,
              amlShuftiReferenceId: currentShareholderData.amlShuftiReferenceId,
              amlShuftiCallbackData: currentShareholderData.amlShuftiCallbackData,
              videoKyc: { ...(currentShareholderData.videoKyc || {}) },
              addressVerification: { ...(currentShareholderData.addressVerification || {}) },
              documentKycStatus: currentShareholderData.documentKycStatus,
              documentKycNotes: currentShareholderData.documentKycNotes,
              documentKycShuftiReferenceId: currentShareholderData.documentKycShuftiReferenceId,
              documentKycShuftiCallbackData: currentShareholderData.documentKycShuftiCallbackData,
            };

            if (fieldPath.startsWith('videoKyc.')) {
              fullShareholderPayload.videoKyc = { ...fullShareholderPayload.videoKyc, ...updatedFieldsOnly.videoKyc };
            } else if (fieldPath.startsWith('addressVerification.')) {
              fullShareholderPayload.addressVerification = { ...fullShareholderPayload.addressVerification, ...updatedFieldsOnly.addressVerification };
            } else {
              Object.assign(fullShareholderPayload, updatedFieldsOnly);
            }
            return fullShareholderPayload;
          } else {
            // Return minimal data for other shareholders as before
            return {
              _id: currentShareholderData._id,
              firstName: currentShareholderData.firstName,
              middleName: currentShareholderData.middleName,
              lastName: currentShareholderData.lastName,
              kycVerificationStatus: currentShareholderData.kycVerificationStatus,
              verificationNotes: currentShareholderData.verificationNotes,
              email: currentShareholderData.email,
              address: currentShareholderData.address,
              shuftiReferenceId: currentShareholderData.shuftiReferenceId,
              amlStatus: currentShareholderData.amlStatus,
              amlNotes: currentShareholderData.amlNotes,
              amlLastChecked: currentShareholderData.amlLastChecked,
              amlShuftiReferenceId: currentShareholderData.amlShuftiReferenceId,
              amlShuftiCallbackData: currentShareholderData.amlShuftiCallbackData,
              videoKyc: currentShareholderData.videoKyc,
              addressVerification: currentShareholderData.addressVerification,
              documentKycStatus: currentShareholderData.documentKycStatus,
              documentKycNotes: currentShareholderData.documentKycNotes,
              documentKycShuftiReferenceId: currentShareholderData.documentKycShuftiReferenceId,
              documentKycShuftiCallbackData: currentShareholderData.documentKycShuftiCallbackData,
            };
          }
        });
        body = { userId, shareholders: shareholdersFullPayload };

      } else {
        throw new Error("Invalid entity type for status update.");
      }

      // --- API call and local state update logic remain the same ---
      const response = await fetch(url, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(body),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: `Request failed with status ${response.status}` }));
        throw new Error(errorData.message || `Failed to update ${fieldPath}`);
      }

      const result = await response.json();
      if (!result.success) {
        throw new Error(result.message || `Server indicated failure for ${fieldPath} update.`);
      }

      // --- Update selectedUser state locally using Immer ---
      // This correctly reflects the change locally *after* the API call succeeds.
      setSelectedUser(currentUser =>
        produce(currentUser, draft => {
          // ... (rest of the Immer update logic is unchanged) ...
          if (!draft) return;

          let targetObjectForUpdate;
          if (entityType === 'user') {
            if (!draft.kyc) draft.kyc = {};
            targetObjectForUpdate = draft.kyc;
          } else { // shareholder
            if (!draft.shareholders || !draft.shareholders[entityIdOrIndex]) {
              console.error("Draft or shareholder at index is missing for Immer update.");
              return;
            }
            targetObjectForUpdate = draft.shareholders[entityIdOrIndex];
          }

          // Navigate and set the primary value
          const keys = fieldPath.split('.');
          let currentLevelInDraft = targetObjectForUpdate;
          for (let i = 0; i < keys.length - 1; i++) {
            const currentKey = keys[i];
            if (!currentLevelInDraft[currentKey]) {
              currentLevelInDraft[currentKey] = {};
            }
            currentLevelInDraft = currentLevelInDraft[currentKey];
          }
          currentLevelInDraft[keys[keys.length - 1]] = newValue;


          // --- Automatically update related date fields (Optimistic UI update) ---
          const nowISO = new Date().toISOString();
          try {
            if (entityType === 'shareholder') {
              const shareholderDraft = draft.shareholders[entityIdOrIndex];
              if (fieldPath === 'videoKyc.status' && shareholderDraft.videoKyc) shareholderDraft.videoKyc.completedOn = nowISO;
              if (fieldPath === 'addressVerification.status' && shareholderDraft.addressVerification) shareholderDraft.addressVerification.verifiedOn = nowISO;
              if (fieldPath === 'amlStatus') shareholderDraft.amlLastChecked = nowISO;
            } else if (entityType === 'user') {
              // Update date fields in the draft based on the fieldPath that was changed
              if (fieldPath === 'videoKyc.status' && draft.kyc?.videoKyc) {
                draft.kyc.videoKyc.completedOn = nowISO;
              } else if (fieldPath === 'businessDetails.businessAddressVerification.status' && draft.kyc?.businessDetails?.businessAddressVerification) {
                // Ensure path exists before setting date
                if (!draft.kyc.businessDetails) draft.kyc.businessDetails = {};
                if (!draft.kyc.businessDetails.businessAddressVerification) draft.kyc.businessDetails.businessAddressVerification = {};
                draft.kyc.businessDetails.businessAddressVerification.verifiedOn = nowISO;
              } else if (fieldPath === 'amlStatus' && draft.kyc) {
                draft.kyc.amlLastChecked = nowISO;
              }
            }
          } catch (dateUpdateError) {
            console.warn("Could not automatically update date field during Immer update:", dateUpdateError);
          }
          // --- End Date Update ---
        }) // end produce
      ); // end setSelectedUser

      setSpecificStatusUpdate({ loadingPath: null, errorPath: null, errorMessage: '' });

    } catch (error) {
      console.error(`Error updating ${fieldPath} for ${entityType} ${entityIdOrIndex !== undefined ? entityIdOrIndex : ''}:`, error);
      setSpecificStatusUpdate({ loadingPath: null, errorPath: loadingKey, errorMessage: error.message || "An unknown error occurred" });
    }
  }; // end handleGenericStatusUpdate

  // --- Rest of the KycApprovalPage component remains unchanged ---

  // ADD THIS FUNCTION INSIDE KycApprovalPage component

  // Find your existing handleShareholderKycUpdate function and wrap it like this:
  const handleShareholderKycUpdate = useCallback(async (shareholderIndex, newStatus) => {
    if (!selectedUser || !selectedUser.shareholders || shareholderIndex < 0 || shareholderIndex >= selectedUser.shareholders.length) {
      console.error("Invalid data for shareholder KYC update.");
      setShareholderUpdateError("Invalid data provided.");
      return;
    }

    const userIdToUpdate = selectedUser._id;

    setUpdatingShareholderIndex(shareholderIndex);
    setShareholderUpdateError(null);

    // Construct the shareholders payload carefully
    // Ensure all fields expected by your 'updateTextData' API are present for ALL shareholders
    const shareholdersPayload = selectedUser.shareholders.map((sh, index) => {
      // Create a base object with all textual and status fields from your ShareholderSchema
      const baseShareholderData = {
        _id: sh._id, // Must include _id for the backend to identify existing shareholders
        firstName: sh.firstName || '',
        middleName: sh.middleName || '',
        lastName: sh.lastName || '',
        kycVerificationStatus: sh.kycVerificationStatus, // This will be overridden for the target shareholder
        verificationNotes: sh.verificationNotes || '',
        email: sh.email || '',
        address: {
          zone: sh.address?.zone || '',
          streetNo: sh.address?.streetNo || '',
          buildingNo: sh.address?.buildingNo || '',
          floorNo: sh.address?.floorNo || '',
          unitNo: sh.address?.unitNo || '',
          additionalLandmark: sh.address?.additionalLandmark || ''
        },
        shuftiReferenceId: sh.shuftiReferenceId, // Main Shufti ID
        amlStatus: sh.amlStatus,
        amlNotes: sh.amlNotes,
        amlLastChecked: sh.amlLastChecked,
        amlShuftiReferenceId: sh.amlShuftiReferenceId,
        // amlShuftiCallbackData: sh.amlShuftiCallbackData, // Schema.Types.Mixed - send if API expects full object
        kycExpirationDate: sh.kycExpirationDate,
        videoKyc: { // Ensure nested objects are structured as expected by API
          status: sh.videoKyc?.status,
          shuftiReferenceId: sh.videoKyc?.shuftiReferenceId,
          initiatedOn: sh.videoKyc?.initiatedOn,
          completedOn: sh.videoKyc?.completedOn,
          // shuftiCallbackData: sh.videoKyc?.shuftiCallbackData,
          declineReason: sh.videoKyc?.declineReason,
          reviewNotes: sh.videoKyc?.reviewNotes,
          recordingUrl: sh.videoKyc?.recordingUrl,
        },
        addressVerification: {
          status: sh.addressVerification?.status,
          shuftiReferenceId: sh.addressVerification?.shuftiReferenceId,
          // shuftiCallbackData: sh.addressVerification?.shuftiCallbackData,
          verifiedOn: sh.addressVerification?.verifiedOn,
          verificationNotes: sh.addressVerification?.verificationNotes,
          rejectionReason: sh.addressVerification?.rejectionReason,
        },
        documentKycStatus: sh.documentKycStatus,
        documentKycNotes: sh.documentKycNotes,
        documentKycShuftiReferenceId: sh.documentKycShuftiReferenceId,
        // documentKycShuftiCallbackData: sh.documentKycShuftiCallbackData,
        dataClassificationType: sh.dataClassificationType || 'internal', // Default if not present
        // IMPORTANT: DO NOT include the full 'passport', 'qid', 'proofOfAddress' DocumentSchema objects
        // unless your API specifically handles merging them. This endpoint is 'updateTextData'.
      };

      if (index === shareholderIndex) {
        return {
          ...baseShareholderData,
          kycVerificationStatus: newStatus, // Apply the new overall KYC status
        };
      }
      return baseShareholderData; // Send the full data for other shareholders as well
    });

    try {
      const response = await fetch(`${config.apiUrl}/ops/invoiceFinancing/shareholders/updateTextData`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: userIdToUpdate,
          shareholders: shareholdersPayload, // Send the whole updated array
        }),
      });

      if (!response.ok) {
        let errorMsg = 'Failed to update shareholder KYC status';
        try { const errorData = await response.json(); errorMsg = errorData.message || errorMsg; } catch (e) {
          console.log(e);
        }
        throw new Error(errorMsg);
      }

      const result = await response.json();
      if (!result.success) {
        throw new Error(result.message || 'Shareholder update failed according to server.');
      }

      // Update selectedUser state locally using Immer
      setSelectedUser(currentUser =>
        produce(currentUser, draft => {
          if (draft.shareholders && draft.shareholders[shareholderIndex]) {
            draft.shareholders[shareholderIndex].kycVerificationStatus = newStatus;
            // Optionally, update a 'modifiedOn' timestamp here if your schema has it
            // draft.shareholders[shareholderIndex].modifiedOn = new Date().toISOString();
          }
        })
      );
    } catch (error) {
      console.error(`Error updating KYC for shareholder ${shareholderIndex + 1}:`, error);
      setShareholderUpdateError(`Failed: ${error.message}`);
    } finally {
      setUpdatingShareholderIndex(null);
    }
  }, [selectedUser, setSelectedUser, setUpdatingShareholderIndex, setShareholderUpdateError, config.apiUrl]); // Add dependencies

  // --- START: useEffect for Shareholder Auto-Approval ---
  useEffect(() => {
    if (selectedUser && selectedUser.shareholders && Array.isArray(selectedUser.shareholders)) {
      // console.log("Auto-approval useEffect triggered for selectedUser:", selectedUser._id);
      selectedUser.shareholders.forEach((shareholder, index) => {
        const currentOverallKycStatus = getNested(shareholder, 'kycVerificationStatus');

        // Only proceed if not already 'APPROVED' to avoid unnecessary checks/updates
        if (currentOverallKycStatus !== 'APPROVED') {
          const allComponentsMet = checkShareholderAllComponentsApproved(shareholder);

          if (allComponentsMet) {
            console.log(
              `AUTO-UPDATE TRIGGERED: Shareholder ${index + 1} (${getNested(shareholder, 'firstName', '')} ${getNested(shareholder, 'lastName', '')}) ` +
              `meets all criteria for auto-approval. Current status: ${currentOverallKycStatus}. Updating to APPROVED.`
            );
            // Call the memoized function to update the shareholder's overall KYC status
            handleShareholderKycUpdate(index, 'APPROVED');
          }
        }
      });
    }
  }, [selectedUser, handleShareholderKycUpdate]); // Dependencies: selectedUser and the memoized update function
  // --- END: useEffect for Shareholder Auto-Approval ---

  const handleDownloadDocumentsZip = async () => {
    if (!selectedUser) return;

    setIsGeneratingPdf(true); // Indicates processing
    console.log("Starting document zip generation with watermarking...");

    const zip = new JSZip();
    const documentsToFetch = [];
    const failedDocs = [];

    const sanitizeFilename = (name) => {
      if (!name) return 'unnamed_document';
      return name
        .replace(/[\/\\:*?"<>|]/g, '_')
        .replace(/[\x00-\x1f\x7f]/g, '_')
        .replace(/_+/g, '_')
        .replace(/\s+/g, '_')
        .replace(/^_|_$/g, '');
    };

    const addDocToList = (path, categoryLabel, labelPrefix = '') => {
      const docData = getNested(selectedUser, path, null);
      if (docData && docData.signedUrl && docData.filePath) {
        const uploadedFileName = docData.filePath.split('/').pop() || `file_${path.replace(/\./g, '_')}`;
        const docName = labelPrefix + path.split('.').pop();

        documentsToFetch.push({
          url: docData.signedUrl,
          zipFileName: `${sanitizeFilename(docName)}_${sanitizeFilename(uploadedFileName)}`,
          originalPath: path,
          label: docName,
          uploadedFileName: uploadedFileName,
        });
        console.log(`Scheduled fetch for: ${uploadedFileName} (as ${docName}) from ${path}`);
      } else {
        console.log(`Document not found or no URL/filePath for path: ${path}`);
      }
    };

    // --- Define Document Fields (Keep your existing definitions) ---
    const businessDocFields = [
      { key: 'commercialRegistration', label: 'Commercial Registration', isRequired: true },
      { key: 'tradeLicense', label: 'Trade License', isRequired: true },
      { key: 'taxCard', label: 'Tax Card', isRequired: true },
      { key: 'establishmentCard', label: 'Establishment Card', isRequired: true },
      { key: 'memorandumOfAssociation', label: 'Memorandum of Association', isRequired: true },
      { key: 'articleOfAssociation', label: 'Article of Association', isRequired: true },
      { key: 'commercialCreditReport', label: 'Commercial Credit Report', isRequired: false },
      { key: 'otherDocument', label: 'Other Document 1', isRequired: false },
      { key: 'otherDocumentTwo', label: 'Other Document 2', isRequired: false },
      { key: 'otherDocument3', label: 'Other Document 3', isRequired: false },
      { key: 'otherDocument4', label: 'Other Document 4', isRequired: false },
      { key: 'otherDocument5', label: 'Other Document 5', isRequired: false },
      { key: 'otherDocument6', label: 'Other Document 6', isRequired: false },
      { key: 'otherDocument7', label: 'Other Document 7', isRequired: false },
      { key: 'otherDocument8', label: 'Other Document 8', isRequired: false },
      { key: 'otherDocument9', label: 'Other Document 9', isRequired: false },
      { key: 'otherDocument10', label: 'Other Document 10', isRequired: false },
      // { key: 'ccrDocument', label: 'CCR Document (Alt)', isRequired: false },
    ];
    const financialDocFields = [
      { key: 'bankStatement', label: 'Bank Statement (Last 6 months)', isRequired: true },
      { key: 'auditedFinancialReport', label: 'Audited Financial Report (12mo)', isRequired: true },
      { key: 'cashFlowLedger', label: 'Cash Flow / Ledger', isRequired: true },
    ];
    // --- End Document Fields ---

    // --- Populate the list of documents to fetch (Keep your existing logic) ---
    console.log("Collecting document paths...");
    businessDocFields.forEach(f => addDocToList(f.key, 'Business Documents', f.label + ' '));
    financialDocFields.forEach(f => addDocToList(f.key, 'Financial Documents', f.label + ' '));
    addDocToList('kyc.qatariId', 'KYC Personal', 'Qatari ID ');
    addDocToList('kyc.passport', 'KYC Personal', 'Passport ');
    addDocToList('kyc.utilityBill', 'KYC Personal', 'Utility Bill ');
    addDocToList('kyc.employmentDetails.employmentLetter', 'KYC Employment/Income', 'Employment Letter ');
    addDocToList('kyc.incomeDetails.proofOfIncome', 'KYC Employment/Income', 'Proof of Income ');

    (getNested(selectedUser, 'shareholders', []) || []).forEach((sh, index) => {
      const prefix = `Shareholder ${index + 1} `;
      addDocToList(`shareholders.${index}.passport`, 'Shareholders', prefix + 'Passport ');
      addDocToList(`shareholders.${index}.qid`, 'Shareholders', prefix + 'QID ');
      addDocToList(`shareholders.${index}.proofOfAddress`, 'Shareholders', prefix + 'Proof of Address ');
    });
    (getNested(selectedUser, 'kyc.directors', []) || []).forEach((dir, index) => {
      addDocToList(`kyc.directors.${index}.idDocument`, 'Directors', `Director ${index + 1} ID Document `);
    });
    (getNested(selectedUser, 'authorizedSignatories', []) || []).forEach((sig, index) => {
      addDocToList(`authorizedSignatories.${index}.idDocument`, 'Authorized Signatories', `Signatory ${index + 1} ID Document `);
    });
    (getNested(selectedUser, 'beneficialOwners', []) || []).forEach((owner, index) => {
      addDocToList(`beneficialOwners.${index}.idDocument`, 'Beneficial Owners', `Owner ${index + 1} ID Document `);
    });
    (getNested(selectedUser, 'kyc.buyers', []) || []).forEach((buyer, index) => {
      addDocToList(`kyc.buyers.${index}.companyDocument`, 'Buyers', `Buyer ${index + 1} Company Document `);
    });

    console.log(`Found ${documentsToFetch.length} documents to potentially download.`);

    if (documentsToFetch.length > 0) {
      console.log("Fetching documents and adding to ZIP...");
      const results = await Promise.allSettled(
        documentsToFetch.map(async (docInfo) => {
          try {
            console.log(`Downloading: ${docInfo.uploadedFileName} (URL: ${docInfo.url})`);
            const response = await fetch(docInfo.url);
            if (!response.ok) {
              throw new Error(`Download failed for ${docInfo.uploadedFileName} with status ${response.status}`);
            }
            let fileBlob = await response.blob();

            // --- WATERMARKING LOGIC START ---
            const isPdf = docInfo.zipFileName.toLowerCase().endsWith('.pdf') || fileBlob.type === 'application/pdf';

            if (isPdf) {
              console.log(`Attempting to watermark: ${docInfo.zipFileName}`);
              try {
                const arrayBuffer = await fileBlob.arrayBuffer();
                const pdfDoc = await PDFDocument.load(arrayBuffer);
                const helveticaFont = await pdfDoc.embedFont(StandardFonts.Helvetica); // You can embed custom fonts too

                const pages = pdfDoc.getPages();
                const watermarkText = `Restricted`;

                for (const page of pages) {
                  const { width, height } = page.getSize();
                  page.drawText(watermarkText, {
                    x: width / 2 - (watermarkText.length * 3), // Adjust X for centering (approx)
                    y: height / 2, // Centered vertically
                    size: 32,      // Adjust font size
                    font: helveticaFont,
                    color: rgb(0.6, 0.6, 0.6), // Light grey, less intrusive
                    opacity: 0.4,             // Adjust opacity
                    rotate: degrees(-45),     // Rotate watermark
                  });
                }
                const pdfBytes = await pdfDoc.save();
                fileBlob = new Blob([pdfBytes], { type: 'application/pdf' });
                console.log(`Successfully watermarked: ${docInfo.zipFileName}`);
              } catch (watermarkError) {
                console.error(`Failed to apply watermark to ${docInfo.zipFileName}:`, watermarkError);
                failedDocs.push(`${docInfo.zipFileName} (Watermark failed: ${watermarkError.message})`);
                // If watermarking fails, the original blob will be used.
              }
            }
            // --- WATERMARKING LOGIC END ---

            if (fileBlob.size === 0) {
              throw new Error(`Downloaded empty file for ${docInfo.uploadedFileName}`);
            }
            console.log(`Adding to zip: ${docInfo.zipFileName}, size: ${fileBlob.size} bytes`);
            zip.file(docInfo.zipFileName, fileBlob);
            return { status: 'fulfilled', path: docInfo.originalPath };
          } catch (error) {
            console.error(`Failed processing ${docInfo.uploadedFileName} (Path: ${docInfo.originalPath}):`, error);
            return { status: 'rejected', path: docInfo.originalPath, reason: error.message || 'Unknown error' };
          }
        })
      );

      results.forEach(result => {
        if (result.status === 'rejected') {
          const failedDocInfo = documentsToFetch.find(d => d.originalPath === result.path);
          failedDocs.push(`${failedDocInfo?.zipFileName || result.path} (Download/Processing Error: ${result.reason})`);
        }
      });

      if (failedDocs.length > 0) {
        console.warn("Some documents failed to download, watermark, or add to zip:", failedDocs);
        alert(`Warning: Could not properly process the following documents:\n\n${failedDocs.join('\n')}\n\nThe ZIP file will be generated with the successfully processed documents.`);
      }

      console.log("Generating ZIP file...");
      try {
        const zipBlob = await zip.generateAsync(
          { type: 'blob', compression: "DEFLATE", compressionOptions: { level: 6 } },
          (metadata) => { if (metadata) { console.log("Zip progress:", metadata.percent.toFixed(2) + '%'); } }
        );
        const zipFileNameToSave = `kyc_documents_${sanitizeFilename(getNested(selectedUser, 'firstName', 'user'))}_${sanitizeFilename(getNested(selectedUser, 'lastName', ''))}_${selectedUser._id}.zip`;
        saveAs(zipBlob, zipFileNameToSave);
        console.log("ZIP generation complete and download initiated.");
      } catch (zipError) {
        console.error('Error generating ZIP file:', zipError);
        alert(`Failed to generate the final ZIP file: ${zipError.message}`);
      }
    } else {
      console.log("No documents found to include in the ZIP.");
      alert("No documents were found for this user to download.");
    }
    setIsGeneratingPdf(false);
  };

  const fetchUsers = async () => {
    setLoading(true);
    setFetchError(null);
    setNoUsersFound(false);
    try {
      const response = await fetch(`${config.apiUrl}/ops/invoiceFinancing/getSubmittedKycs`);
      if (!response.ok) {
        let errorBody = null;
        try { errorBody = await response.json(); } catch (_) { console.log("error", _); }
        throw new Error(errorBody?.message || `Network response was not ok: ${response.status}`);
      }
      const data = await response.json();
      if (data.success && data.kycs) {
        if (data.kycs.length > 0) {
          setUsers(data.kycs);
        } else {
          setNoUsersFound(true);
          setUsers([]);
        }
      } else {
        setNoUsersFound(true);
        setUsers([]);
        console.warn("API response indicates failure or missing data:", data.message);
      }
    } catch (error) {
      console.error('Error fetching users:', error);
      setFetchError(error.message);
      setUsers([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, []);

  const handleCloseModal = () => {
    setShowModal(false); // Close the modal
    fetchUsers(); // Fetch users immediately after closing
  };

  const handleViewDetails = (user) => {
    setSelectedUser(user);
    setOverallVerificationStatus(getNested(user, 'kyc.verificationStatus', 'INITIATED'));
    setOverallVerificationNotes(getNested(user, 'kyc.verificationNotes', ''));
    setActiveTab('businessDocs'); // Reset to new default tab
    setShowModal(true);
  };

  const handleDocumentUpdateSuccess = useCallback((documentFieldPath, updatedDocumentData) => {
    setSelectedUser(currentUser => {
      if (!currentUser) return null;

      // Use Immer for safe and easy nested state updates
      return produce(currentUser, draft => {
        // Helper to set nested value based on path
        const setNestedValue = (obj, path, value) => {
          const keys = path.split('.');
          let current = obj;
          for (let i = 0; i < keys.length - 1; i++) {
            const key = keys[i];
            // Check if the key is an array index
            const index = parseInt(key, 10);
            if (!isNaN(index) && Array.isArray(current)) {
              // Ensure the index exists in the draft array
              if (!current[index]) {
                console.warn(`Attempted to update non-existent index ${index} in path ${path}`);
                return; // Or create default object structure if needed
              }
              current = current[index];
            } else {
              // Ensure the object path exists
              if (!current[key]) {
                current[key] = {}; // Create nested object if it doesn't exist
              }
              current = current[key];
            }
          }
          // Set the final value
          current[keys[keys.length - 1]] = value;
        };

        setNestedValue(draft, documentFieldPath, updatedDocumentData);
      });
    });
    // Optional: Show a brief success message to the user
    // console.log(`Successfully updated ${documentFieldPath}`);
  }, []);

  const handleUpdateOverallKyc = async () => {
    if (!selectedUser) return;
    const userIdToUpdate = selectedUser._id;
    if (!userIdToUpdate) {
      alert('Cannot update KYC: User ID is missing.');
      return;
    }

    // --- START: Updated Check Before Approving ---
    if (overallVerificationStatus === 'APPROVED') {

      // 1. Check individual document statuses (as before)
      const allDocsVerified = checkAllRequiredDocumentsVerified(selectedUser);

      // 2. Check all required KYC/AML/Video/Address statuses for user and shareholders
      const statusCheckResult = checkAllRequiredStatusesPass(selectedUser);

      // 3. Combine checks and provide specific feedback if failure occurs
      if (!allDocsVerified || !statusCheckResult.passes) {
        let alertMessage = 'Cannot approve overall KYC. Please check the verification statuses:\n\n';

        // Add message for document verification failure
        if (!allDocsVerified) {
          alertMessage += '- Not all required documents have been uploaded and verified. Please review individual document statuses.\n';
        }

        // Add messages for specific status failures
        if (!statusCheckResult.passes) {
          statusCheckResult.failures.forEach(failure => {
            alertMessage += `- ${failure.entityDescription}: ${failure.label} is currently "${failure.current}". It must be "${failure.expected}".\n`;
          });
        }

        alert(alertMessage.trim());

        // Optionally revert the overall status dropdown selection if the check fails
        // setOverallVerificationStatus(getNested(selectedUser, 'kyc.verificationStatus', 'INITIATED'));

        return; // Stop the update process
      }
      // If both checks pass, continue to the update API call
    }
    // --- END: Updated Check Before Approving ---

    // Proceed with the update if status is not 'APPROVED' or if all checks passed
    setIsUpdating(true);

    try {
      const response = await fetch(`${config.apiUrl}/ops/invoiceFinancing/updateKyc`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json', },
        body: JSON.stringify({
          id: userIdToUpdate,
          kyc: { // Send only the fields being updated by this specific action
            verificationStatus: overallVerificationStatus,
            verificationNotes: overallVerificationNotes,
            // Include verifiedOn timestamp if status is APPROVED
            ...(overallVerificationStatus === 'APPROVED' && { verifiedOn: new Date().toISOString() })
          },
        }),
      });
      if (!response.ok) {
        let errorMsg = 'Failed to update overall KYC status';
        try { const errorData = await response.json(); errorMsg = errorData.message || errorMsg; } catch (_) { console.log(_); }
        throw new Error(errorMsg);
      }
      // Success

      // If the status is APPROVED then generate CAM file
      if (overallVerificationStatus === 'APPROVED') {
        try {
          console.log(`Generating Credit report for user: ${userIdToUpdate}`);
          const camResponse = await fetch(`${config.apiUrl}/ops/invoiceFinancing/generateCreditAssessmentReport`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              userId: userIdToUpdate
            })
          });

          if (!camResponse.ok) {
            console.error(`Error generating Credit report: ${camResponse.status}`);
            alert(`KYC status updated to APPROVED, but there was an issue generating the CAM report. Please check the logs.`);
          } else {
            console.log(`Credit report generation initiated for user: ${userIdToUpdate}`);
          }
        } catch (camError) {
          console.error(`Error generating Credit report:`, camError);
          alert(`KYC status updated to APPROVED, but there was an error generating the CAM report: ${camError.message}`);
        }
      }

      await fetchUsers(); // Refetch users list to reflect the change
      setShowModal(false); // Close the modal
      alert(`Overall KYC status successfully updated to ${overallVerificationStatus}.${overallVerificationStatus === 'APPROVED' ? ' Credit report generation has been initiated.' : ''}`); // Provide success feedback

    } catch (error) {
      console.error('Error updating overall KYC:', error);
      alert(`Failed to update overall KYC status: ${error.message}. Please try again.`);
    }
    finally {
      setIsUpdating(false);
    }
  };

  const renderInfoItem = (label, value, isDocumentLink = false) => {
    let displayValue = value;

    if (isDocumentLink) {
      // Assuming value is already a React element from RenderDocumentDisplay
      displayValue = value;
    } else {
      if (typeof value === 'boolean') { displayValue = value ? 'Yes' : 'No'; }
      if (['Date of Birth', 'Inserted On', 'Updated On'].includes(label)) { displayValue = formatDate(value); }
      if (['Monthly Income', 'Monthly Expenses', 'Monthly Turnover'].includes(label) && typeof value === 'number') { displayValue = `QAR ${value.toLocaleString()}`; }
      if (displayValue === null || displayValue === undefined || displayValue === '' || displayValue === 'N/A') { displayValue = <span className="text-gray-500 italic">Not Provided</span>; }
    }

    return (
      <div className="py-1.5"> {/* Slightly tighter padding */}
        <dt className="text-xs font-medium text-gray-500 uppercase tracking-wide">{label}</dt>
        <dd className="mt-0.5 text-sm text-gray-900 break-words">{displayValue}</dd>
      </div>
    );
  };

  const filteredUsers = useMemo(() => {
    // Ensure 'users' is defined and an array before filtering
    if (!users || !Array.isArray(users)) {
      return [];
    }

    const { searchTerm, startDate, endDate, kycStatus, amlStatus, businessType, sector, isActive } = kycFilters;
    const lowerSearchTerm = searchTerm.toLowerCase();
    const tsStartDate = startDate ? new Date(startDate).setHours(0, 0, 0, 0) : null;
    const tsEndDate = endDate ? new Date(endDate).setHours(23, 59, 59, 999) : null;

    return users.filter(user => {
      const userName = `${getNested(user, 'firstName', '')} ${getNested(user, 'lastName', '')}`.toLowerCase();
      const userEmail = getNested(user, 'email', '').toLowerCase();
      const crNumber = getNested(user, 'kyc.businessDetails.crNumber', '').toLowerCase();
      const userKycStatus = getNested(user, 'kyc.verificationStatus', 'INITIATED');
      const userAmlStatus = getNested(user, 'kyc.amlStatus', 'NOT_CHECKED');
      const userBusinessType = getNested(user, 'kyc.businessDetails.businessType', '');
      const userSector = getNested(user, 'kyc.businessDetails.sector', '');
      const userIsActive = getNested(user, 'isActive', null); // Get original boolean or null

      const submissionDate = user.insertedOn ? new Date(user.insertedOn).getTime() : null;

      // Search Term Filter (Name, Email, CR Number)
      if (searchTerm && !(userName.includes(lowerSearchTerm) || userEmail.includes(lowerSearchTerm) || crNumber.includes(lowerSearchTerm))) {
        return false;
      }

      // Date Range Filter (Submission Date)
      if (tsStartDate && (!submissionDate || submissionDate < tsStartDate)) {
        return false;
      }
      if (tsEndDate && (!submissionDate || submissionDate > tsEndDate)) {
        return false;
      }

      // KYC Status Filter
      if (kycStatus && userKycStatus !== kycStatus) {
        return false;
      }

      // AML Status Filter
      if (amlStatus && userAmlStatus !== amlStatus) {
        return false;
      }

      // Business Type Filter
      if (businessType && userBusinessType !== businessType) {
        return false;
      }

      // Sector Filter
      if (sector && userSector !== sector) {
        return false;
      }

      // Is Active Filter (handle 'true'/'false' string values from select)
      if (isActive !== '') { // Only apply if a value is selected ('true' or 'false')
        if (isActive === 'true' && userIsActive !== true) {
          return false;
        }
        if (isActive === 'false' && userIsActive !== false) {
          return false;
        }
      }

      return true; // Passed all filters
    });
  }, [users, kycFilters]); // Depend on 'users' (the raw data) and 'kycFilters'

  const sortedUsers = useMemo(() => {
    // Now correctly sort the already filtered users
    return [...filteredUsers].sort((a, b) => {
      const statusOrder = { 'APPROVED': 5, 'REVIEW': 3, 'UNDER_REVIEW': 0, 'INITIATED': 1, 'REINITIATED': 4, 'REJECTED': 3 };
      const statusA = getNested(a, 'kyc.verificationStatus', 'INITIATED');
      const statusB = getNested(b, 'kyc.verificationStatus', 'INITIATED');
      const orderA = statusOrder[statusA] ?? 6; // Use nullish coalescing for default
      const orderB = statusOrder[statusB] ?? 6;

      if (orderA !== orderB) {
        return orderA - orderB;
      }

      const dateA = new Date(getNested(a, 'insertedOn', 0));
      const dateB = new Date(getNested(b, 'insertedOn', 0)); // Corrected to use getNested for consistency
      const timeA = !isNaN(dateA.getTime()) ? dateA.getTime() : 0;
      const timeB = !isNaN(dateB.getTime()) ? dateB.getTime() : 0;
      return timeB - timeA;
    });
  }, [filteredUsers]); // This must now depend on `filteredUsers`


  // --- Loading / Error / No Data States ---
  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (fetchError) {
    return (
      <div className="p-6 bg-red-50 text-red-800 rounded-lg max-w-2xl mx-auto mt-10">
        <h2 className="text-xl font-bold mb-2">Error</h2>
        <p>Could not fetch KYC submissions: {fetchError}</p>
        <button
          onClick={fetchUsers}
          className="mt-4 bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 text-sm font-medium"
        >
          Try Again
        </button>
      </div>
    );
  }

  if (noUsersFound || !users || !Array.isArray(users) || users.length === 0) {
    return (
      <div className="p-6 bg-blue-50 text-blue-800 rounded-lg max-w-lg mx-auto mt-10 text-center">
        <h2 className="text-xl font-bold mb-2">No Submissions</h2>
        <p>There are currently no KYC submissions requiring review.</p>
      </div>
    );
  }


  // Helper to add document fields to a row object
  const addDocumentFieldsToRow = (rowData, docObject, prefix, fieldPathForDoc, getNested, formatDate) => {
    if (!docObject) { // If the document data itself is null/undefined
      rowData[`${prefix} - File Name`] = 'N/A';
      rowData[`${prefix} - Uploaded On`] = 'N/A';
      rowData[`${prefix} - Status`] = 'NOT_UPLOADED';
      rowData[`${prefix} - Notes`] = '';
      rowData[`${prefix} - Verification Date`] = 'N/A';
      rowData[`${prefix} - Signed URL`] = 'N/A'; // Added for consistency when docObject is null
      return;
    }
    const filePath = getNested(docObject, 'filePath', '');
    rowData[`${prefix} - File Name`] = filePath ? filePath.split('/').pop() : 'N/A';
    rowData[`${prefix} - Uploaded On`] = formatDate(getNested(docObject, 'uploadedOn'));
    rowData[`${prefix} - Status`] = getNested(docObject, 'verificationStatus', 'PENDING');
    rowData[`${prefix} - Notes`] = getNested(docObject, 'verificationNotes', '');
    rowData[`${prefix} - Verification Date`] = formatDate(getNested(docObject, 'verifiedOrRejectedOn'));
    rowData[`${prefix} - Signed URL`] = getNested(docObject, 'signedUrl', 'N/A'); // <-- MODIFIED: Added Signed URL
  };

  const flattenUserData = (user, maxShareholders, maxDirectors, maxBuyers, businessDocFields, financialDocFields, getNested, formatDate, addDocumentFieldsToRow) => {
    const rowData = {};

    // Basic User Info
    rowData['User ID'] = user._id;
    rowData['First Name'] = getNested(user, 'firstName', 'N/A');
    rowData['Middle Name'] = getNested(user, 'middleName', 'N/A');
    rowData['Last Name'] = getNested(user, 'lastName', 'N/A');
    rowData['Email'] = getNested(user, 'email', 'N/A');
    rowData['Mobile Number'] = getNested(user, 'mobileNo', 'N/A');
    rowData['Inserted On'] = formatDate(getNested(user, 'insertedOn'));
    rowData['Last Updated On'] = formatDate(getNested(user, 'updatedOn'));
    rowData['Is Active'] = getNested(user, 'isActive', 'N/A');

    // Overall KYC (from 'verification' tab perspective, usually root kyc object)
    rowData['Overall KYC Status'] = getNested(user, 'kyc.verificationStatus', 'N/A');
    rowData['Overall KYC Notes'] = getNested(user, 'kyc.verificationNotes', '');
    rowData['Overall KYC Verified On'] = formatDate(getNested(user, 'kyc.verifiedOn'));

    // --- Tab: Business Documents ---
    businessDocFields.forEach(field => {
      const docData = getNested(user, field.key);
      addDocumentFieldsToRow(rowData, docData, `Business Doc - ${field.label}`, field.key, getNested, formatDate);
    });

    // --- Tab: Financials ---
    financialDocFields.forEach(field => {
      const docData = getNested(user, field.key);
      addDocumentFieldsToRow(rowData, docData, `Financial Doc - ${field.label}`, field.key, getNested, formatDate);
    });

    // --- Tab: Shareholders ---
    const shareholders = getNested(user, 'shareholders', []) || [];
    for (let i = 0; i < maxShareholders; i++) {
      const sh = shareholders[i];
      const prefix = `Shareholder ${i + 1}`;
      if (sh) {
        rowData[`${prefix} - First Name`] = getNested(sh, 'firstName', 'N/A');
        rowData[`${prefix} - Middle Name`] = getNested(sh, 'middleName', 'N/A');
        rowData[`${prefix} - Last Name`] = getNested(sh, 'lastName', 'N/A');
        rowData[`${prefix} - Email`] = getNested(sh, 'email', 'N/A');
        rowData[`${prefix} - Overall Notes`] = getNested(sh, 'verificationNotes', '');
        rowData[`${prefix} - Address Zone`] = getNested(sh, 'address.zone', 'N/A');
        rowData[`${prefix} - Address Street`] = getNested(sh, 'address.streetNo', 'N/A');
        rowData[`${prefix} - Address Building`] = getNested(sh, 'address.buildingNo', 'N/A');
        rowData[`${prefix} - Address Floor`] = getNested(sh, 'address.floorNo', 'N/A');
        rowData[`${prefix} - Address Unit`] = getNested(sh, 'address.unitNo', 'N/A');
        rowData[`${prefix} - KYC Status`] = getNested(sh, 'kycVerificationStatus', 'N/A');
        rowData[`${prefix} - AML Status`] = getNested(sh, 'amlStatus', 'N/A');
        rowData[`${prefix} - AML Last Checked`] = formatDate(getNested(sh, 'amlLastChecked'));
        rowData[`${prefix} - AML Notes`] = getNested(sh, 'amlNotes', '');
        rowData[`${prefix} - AML Shufti Ref ID`] = getNested(sh, 'amlShuftiReferenceId', 'N/A');

        rowData[`${prefix} - Video KYC Status`] = getNested(sh, 'videoKyc.status', 'N/A');
        rowData[`${prefix} - Video KYC Completed On`] = formatDate(getNested(sh, 'videoKyc.completedOn'));
        rowData[`${prefix} - Video KYC Review Notes`] = getNested(sh, 'videoKyc.reviewNotes', '');
        rowData[`${prefix} - Video KYC Shufti Ref ID`] = getNested(sh, 'videoKyc.shuftiReferenceId', 'N/A');
        rowData[`${prefix} - Video KYC Decline Reason`] = getNested(sh, 'videoKyc.declineReason', 'N/A');
        rowData[`${prefix} - Video KYC Recording URL`] = getNested(sh, 'videoKyc.recordingUrl', 'N/A');

        rowData[`${prefix} - Address Verification Status`] = getNested(sh, 'addressVerification.status', 'N/A');
        rowData[`${prefix} - Address Verification Verified On`] = formatDate(getNested(sh, 'addressVerification.verifiedOn'));
        rowData[`${prefix} - Address Verification Notes`] = getNested(sh, 'addressVerification.verificationNotes', '');
        rowData[`${prefix} - Address Verification Shufti Ref ID`] = getNested(sh, 'addressVerification.shuftiReferenceId', 'N/A');
        rowData[`${prefix} - Address Verification Rejection Reason`] = getNested(sh, 'addressVerification.rejectionReason', 'N/A');

        rowData[`${prefix} - Document Set KYC Status`] = getNested(sh, 'documentKycStatus', 'N/A');
        rowData[`${prefix} - Document Set KYC Notes`] = getNested(sh, 'documentKycNotes', '');
        rowData[`${prefix} - Document Set Shufti Ref ID`] = getNested(sh, 'documentKycShuftiReferenceId', 'N/A');

        addDocumentFieldsToRow(rowData, getNested(sh, 'passport'), `${prefix} - Passport`, `shareholders.${i}.passport`, getNested, formatDate);
        addDocumentFieldsToRow(rowData, getNested(sh, 'qid'), `${prefix} - QID`, `shareholders.${i}.qid`, getNested, formatDate);
        addDocumentFieldsToRow(rowData, getNested(sh, 'proofOfAddress'), `${prefix} - Proof of Address`, `shareholders.${i}.proofOfAddress`, getNested, formatDate);
      } else {
        // Add blank fields if shareholder doesn't exist for this index
        [
          'First Name', 'Middle Name', 'Last Name', 'Email', 'Overall Notes',
          'Address Zone', 'Address Street', 'Address Building', 'Address Floor', 'Address Unit',
          'KYC Status', 'AML Status', 'AML Last Checked', 'AML Notes', 'AML Shufti Ref ID',
          'Video KYC Status', 'Video KYC Completed On', 'Video KYC Review Notes', 'Video KYC Shufti Ref ID', 'Video KYC Decline Reason', 'Video KYC Recording URL',
          'Address Verification Status', 'Address Verification Verified On', 'Address Verification Notes', 'Address Verification Shufti Ref ID', 'Address Verification Rejection Reason',
          'Document Set KYC Status', 'Document Set KYC Notes', 'Document Set Shufti Ref ID'
        ].forEach(field => rowData[`${prefix} - ${field}`] = '');
        ['Passport', 'QID', 'Proof of Address'].forEach(docType => {
          ['File Name', 'Uploaded On', 'Status', 'Notes', 'Verification Date', 'Signed URL'].forEach(field => rowData[`${prefix} - ${docType} - ${field}`] = '');
        });
      }
    }

    // --- Tab: Top Buyers ---
    const buyers = getNested(user, 'kyc.buyers', []) || [];
    for (let i = 0; i < maxBuyers; i++) {
      const buyer = buyers[i];
      const prefix = `Buyer ${i + 1}`;
      if (buyer) {
        rowData[`${prefix} - Full Name`] = getNested(buyer, 'buyerName', 'N/A');
        rowData[`${prefix} - Contact Person`] = getNested(buyer, 'contactPerson', 'N/A');
        rowData[`${prefix} - Contact Phone`] = getNested(buyer, 'contactPhone', 'N/A');
        rowData[`${prefix} - Contact Email`] = getNested(buyer, 'contactEmail', 'N/A');
        rowData[`${prefix} - Registration Number`] = getNested(buyer, 'registrationNumber', 'N/A');
        addDocumentFieldsToRow(rowData, getNested(buyer, 'companyDocument'), `${prefix} - Company Document`, `kyc.buyers.${i}.companyDocument`, getNested, formatDate);
      } else {
        ['Full Name', 'Contact Person', 'Contact Phone', 'Contact Email', 'Registration Number'].forEach(field => rowData[`${prefix} - ${field}`] = '');
        ['Company Document'].forEach(docType => {
          ['File Name', 'Uploaded On', 'Status', 'Notes', 'Verification Date', 'Signed URL'].forEach(field => rowData[`${prefix} - ${docType} - ${field}`] = '');
        });
      }
    }

    // --- Tab: EKYC and Summary (Review Tab) ---
    rowData['Business AML Status'] = getNested(user, 'kyc.amlStatus', 'N/A');
    rowData['Business AML Last Checked'] = formatDate(getNested(user, 'kyc.amlLastChecked'));
    rowData['Business Address Verification Status'] = getNested(user, 'kyc.businessDetails.businessAddressVerification.status', 'N/A');
    rowData['Business Address Verified On'] = formatDate(getNested(user, 'kyc.businessDetails.businessAddressVerification.verifiedOn'));
    rowData['Business Address Verification Notes'] = getNested(user, 'kyc.businessDetails.businessAddressVerification.verificationNotes', '');
    rowData['Business Address Rejection Reason'] = getNested(user, 'kyc.businessDetails.businessAddressVerification.rejectionReason', '');

    rowData['KYB Verification Status (CR)'] = getNested(user, 'commercialRegistration.verificationStatus', 'N/A');
    rowData['KYB CR AML Verification Status'] = getNested(user, 'commercialRegistration.shuftiCallbackData.verification_result.aml_for_businesses', 'N/A');

    rowData['Applicant Bank Account Number'] = getNested(user, 'kyc.incomeDetails.accountNumber', 'N/A');
    rowData['Applicant Bank IFSC Code'] = getNested(user, 'kyc.incomeDetails.ifscCode', 'N/A');
    addDocumentFieldsToRow(rowData, getNested(user, 'kyc.employmentDetails.employmentLetter'), 'Applicant Employment Letter', 'kyc.employmentDetails.employmentLetter', getNested, formatDate);
    addDocumentFieldsToRow(rowData, getNested(user, 'kyc.incomeDetails.proofOfIncome'), 'Applicant Proof of Income', 'kyc.incomeDetails.proofOfIncome', getNested, formatDate);

    rowData['Legal Entity Name'] = getNested(user, 'kyc.businessDetails.businessName', 'N/A');
    rowData['CR Number'] = getNested(user, 'kyc.businessDetails.crNumber', 'N/A');
    rowData['Is Qatar Based'] = getNested(user, 'kyc.isQatarBased', 'N/A');
    rowData['Business Type'] = getNested(user, 'kyc.businessDetails.businessType', 'N/A');
    rowData['Sector'] = getNested(user, 'kyc.businessDetails.sector', 'N/A');
    rowData['Year Established'] = getNested(user, 'kyc.businessDetails.yearEstablished', 'N/A');
    rowData['Monthly Turnover'] = getNested(user, 'kyc.businessDetails.monthlyTurnover', 'N/A');
    rowData['Nature of Business'] = getNested(user, 'kyc.businessDetails.natureOfBusiness', 'N/A');
    rowData['Business Address Line 1'] = getNested(user, 'kyc.businessDetails.businessAddressLine1', 'N/A');
    rowData['Business Address Line 2'] = getNested(user, 'kyc.businessDetails.businessAddressLine2', 'N/A');
    rowData['Business City'] = getNested(user, 'kyc.businessDetails.businessCity', 'N/A');
    rowData['Business Country'] = getNested(user, 'kyc.businessDetails.businessCountry', 'N/A');

    // Directors
    const directors = getNested(user, 'kyc.directors', []) || [];
    for (let i = 0; i < maxDirectors; i++) {
      const dir = directors[i];
      const prefix = `Director ${i + 1}`;
      if (dir) {
        rowData[`${prefix} - Name`] = getNested(dir, 'directorName', 'N/A');
        rowData[`${prefix} - Position`] = getNested(dir, 'position', 'N/A');
        rowData[`${prefix} - Nationality`] = getNested(dir, 'nationality', 'N/A');
        rowData[`${prefix} - DOB`] = formatDate(getNested(dir, 'dateOfBirth'));
        rowData[`${prefix} - National ID`] = getNested(dir, 'nationalId', 'N/A');
        rowData[`${prefix} - Contact Person`] = getNested(dir, 'contactPerson', 'N/A');
        rowData[`${prefix} - Contact Phone`] = getNested(dir, 'contactPhone', 'N/A');
        rowData[`${prefix} - Contact Email`] = getNested(dir, 'contactEmail', 'N/A');
        rowData[`${prefix} - Is UBO`] = getNested(dir, 'isUBO', 'N/A');
        rowData[`${prefix} - Is Authorized Signatory`] = getNested(dir, 'isAuthorizedSignatory', 'N/A');
        rowData[`${prefix} - Shareholding %`] = getNested(dir, 'shareholdingPercentage') !== undefined ? `${getNested(dir, 'shareholdingPercentage')}%` : 'N/A';
        rowData[`${prefix} - Address`] = getNested(dir, 'directorAddress', 'N/A');
        addDocumentFieldsToRow(rowData, getNested(dir, 'idDocument'), `${prefix} - ID Document`, `kyc.directors.${i}.idDocument`, getNested, formatDate);
      } else {
        [
          'Name', 'Position', 'Nationality', 'DOB', 'National ID',
          'Contact Person', 'Contact Phone', 'Contact Email',
          'Is UBO', 'Is Authorized Signatory', 'Shareholding %', 'Address'
        ].forEach(field => rowData[`${prefix} - ${field}`] = '');
        ['ID Document'].forEach(docType => {
          ['File Name', 'Uploaded On', 'Status', 'Notes', 'Verification Date', 'Signed URL'].forEach(field => rowData[`${prefix} - ${docType} - ${field}`] = '');
        });
      }
    }

    // --- MODIFIED: Authorized Signatories and Beneficial Owners sections are REMOVED ---

    // Other Personal KYC Documents (Primary Applicant)
    addDocumentFieldsToRow(rowData, getNested(user, 'kyc.qatariId'), 'Applicant Qatari ID', 'kyc.qatariId', getNested, formatDate);
    addDocumentFieldsToRow(rowData, getNested(user, 'kyc.passport'), 'Applicant Passport', 'kyc.passport', getNested, formatDate);
    addDocumentFieldsToRow(rowData, getNested(user, 'kyc.utilityBill'), 'Applicant Utility Bill', 'kyc.utilityBill', getNested, formatDate);

    // Other flags from user root
    rowData['Bank Statement Uploaded Flag'] = getNested(user, 'isBankStatementUploaded', 'N/A');
    rowData['First Invoice Uploaded Flag'] = getNested(user, 'isFirstInvoiceUploaded', 'N/A');
    rowData['Credit Line Assigned Flag'] = getNested(user, 'isCreditLineAssigned', 'N/A');

    return rowData;
  };

  const handleExportData = async () => {
    if (!users || users.length === 0) {
      alert("No user data available to export.");
      return;
    }
    setIsExportingExcel(true);

    try {
      // Determine maximum number of shareholders, directors, buyers for consistent columns
      let maxShareholders = 0;
      let maxDirectors = 0;
      let maxBuyers = 0;

      users.forEach(user => {
        const shareholders = getNested(user, 'shareholders', []) || [];
        if (shareholders.length > maxShareholders) {
          maxShareholders = shareholders.length;
        }
        const directors = getNested(user, 'kyc.directors', []) || [];
        if (directors.length > maxDirectors) {
          maxDirectors = directors.length;
        }
        const buyers = getNested(user, 'kyc.buyers', []) || [];
        if (buyers.length > maxBuyers) {
          maxBuyers = buyers.length;
        }
      });

      if (maxShareholders === 0) maxShareholders = 1;
      if (maxDirectors === 0) maxDirectors = 1;
      if (maxBuyers === 0) maxBuyers = 1;

      const excelData = users.map(user =>
        flattenUserData(user, maxShareholders, maxDirectors, maxBuyers, businessDocFields, financialDocFields, getNested, formatDate, addDocumentFieldsToRow)
      );

      if (excelData.length === 0) {
        alert("No data to export after processing. User list might be empty or filtered out.");
        setIsExportingExcel(false);
        return;
      }

      const worksheet = XLSX.utils.json_to_sheet(excelData);

      if (excelData.length > 0) {
        const headers = Object.keys(excelData[0]);
        const colWidths = headers.map(header => {
          const headerLength = header ? header.toString().length : 10;
          let maxLength = headerLength;

          // The 'maxLength' currently only considers header length.
          // If data in cells is longer, it might still be hidden.
          // For "juuuuuuust enough" for the header, this is fine.

          return { wch: maxLength }; // <-- MODIFIED: Padding reduced from 5 to 2
        });
        worksheet['!cols'] = colWidths;
      }

      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, "KYC Data");

      const now = new Date();
      const timestamp = `${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}_${String(now.getHours()).padStart(2, '0')}${String(now.getMinutes()).padStart(2, '0')}`;
      XLSX.writeFile(workbook, `KYC_Export_${timestamp}.xlsx`);

    } catch (error) {
      console.error("Error exporting data to Excel:", error);
      alert("An error occurred while exporting data. Please check the console.");
    } finally {
      setIsExportingExcel(false);
    }
  };

  const businessDocFields = [
    { key: 'commercialRegistration', label: 'Commercial Registration', isRequired: true },
    { key: 'tradeLicense', label: 'Trade License', isRequired: true },
    { key: 'taxCard', label: 'Tax Card', isRequired: true },
    { key: 'establishmentCard', label: 'Establishment Card', isRequired: true },
    { key: 'memorandumOfAssociation', label: 'Memorandum of Association', isRequired: true },
    { key: 'articleOfAssociation', label: 'Article of Association', isRequired: true },
    { key: 'commercialCreditReport', label: 'Commercial Credit Report', isRequired: false },
    { key: 'otherDocument', label: 'Other Document 1', isRequired: false },
    { key: 'otherDocumentTwo', label: 'Other Document 2', isRequired: false },
    { key: 'otherDocument3', label: 'Other Document 3', isRequired: false },
    { key: 'otherDocument4', label: 'Other Document 4', isRequired: false },
    { key: 'otherDocument5', label: 'Other Document 5', isRequired: false },
    { key: 'otherDocument6', label: 'Other Document 6', isRequired: false },
    { key: 'otherDocument7', label: 'Other Document 7', isRequired: false },
    { key: 'otherDocument8', label: 'Other Document 8', isRequired: false },
    { key: 'otherDocument9', label: 'Other Document 9', isRequired: false },
    { key: 'otherDocument10', label: 'Other Document 10', isRequired: false },
    // Include alternates if needed, decide based on your data priority
    // { key: 'ccrDocument', label: 'CCR Document (Alt)', isRequired: false },
  ];
  const financialDocFields = [
    { key: 'bankStatement', label: 'Bank Statement (Last 6 months)', isRequired: true }, // Assuming root is primary
    // { key: 'kyc.bankStatement', label: 'Bank Statement (KYC)', isRequired: false }, // Secondary if exists
    { key: 'auditedFinancialReport', label: 'Audited Financial Report (12mo)', isRequired: true },
    { key: 'cashFlowLedger', label: 'Cash Flow / Ledger', isRequired: true }, // Assuming root is primary
    // { key: 'cashFlowDocument', label: 'Cash Flow Document (Alt)', isRequired: false }, // Secondary
  ];

  return (
    <div className="p-6 bg-gray-100 min-h-screen">
      <div className="flex justify-between items-center mb-6"> {/* Flex container for title and buttons */}
        {/* Adjusted to ensure title and buttons are in correct positions */}
        <div className="flex items-center space-x-4"> {/* Container for title and potential back button */}
          {/* If you want a "Back to Reports" link, it would go here */}
          <h1 className="text-2xl font-bold text-gray-800">KYC Approvals</h1>
        </div>

        {users && users.length > 0 && ( /* Only show buttons if there's data to export */
          <div className="flex space-x-3"> {/* Container for export buttons */}


            {/* Existing Export to Excel Button */}
            <button
              onClick={handleExportData}
              disabled={isExportingExcel}
              className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 ${isExportingExcel ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              {isExportingExcel ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Exporting...
                </>
              ) : (
                <>
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  Export to Excel
                </>
              )}
            </button>
          </div>
        )}
      </div>

      {/* The NEW KycFilterSection component goes right here, after the header and buttons */}
      <KycFilterSection
        filters={kycFilters}
        setFilters={setKycFilters}
        resetFilters={resetKycFilters}
      />

      {/* The rest of your table and modals follow below */}

      <div className="bg-white p-4 sm:p-6 rounded-lg shadow-md overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mobile</th>
                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Submission Date</th>
                <th scope="col" className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {sortedUsers.map((user) => {
                const status = getNested(user, 'kyc.verificationStatus', 'INITIATED');
                return (
                  <tr key={user._id} className="hover:bg-gray-50">
                    <td className="px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {getNested(user, 'firstName', '')} {getNested(user, 'lastName', '')}
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                      {getNested(user, 'email')}
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                      {getNested(user, 'mobileNo')}
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap">
                      <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${status === 'APPROVED' ? 'bg-green-100 text-green-800' :
                        status === 'REJECTED' ? 'bg-red-100 text-red-800' :
                          status === 'UNDER_REVIEW' ? 'bg-yellow-100 text-yellow-800' :
                            status === 'REVIEW' ? 'bg-purple-100 text-purple-800' :
                              'bg-blue-100 text-blue-800' // Default for INITIATED, REINITIATED etc.
                        }`}>
                        {status}
                      </span>
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatDate(getNested(user, 'insertedOn'))}
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        onClick={() => handleViewDetails(user)}
                        className="text-indigo-600 hover:text-indigo-900 bg-indigo-50 px-3 py-1 rounded-md hover:bg-indigo-100 transition duration-150"
                      >
                        Review
                      </button>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>

      {/* --- KYC Review Modal --- */}
      {showModal && selectedUser && (
        <div className="fixed inset-0 bg-black bg-opacity-60 flex justify-center items-start z-50 p-4 pt-10 overflow-y-auto">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-6xl max-h-[90vh] overflow-hidden flex flex-col"> {/* Increased max-w */}

            {/* Modal Header */}
            <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center sticky top-0 bg-white z-10 flex-shrink-0">
              <h2 className="text-xl font-semibold text-gray-800">
                KYC Review: {getNested(selectedUser, 'firstName', '')} {getNested(selectedUser, 'lastName', '')} (ID: {selectedUser._id})
              </h2>
              <div className="flex items-center space-x-2 flex-shrink-0"> {/* Container for buttons */}
                {/* --- UPDATED DOWNLOAD BUTTON --- */}
                <button
                  onClick={handleDownloadDocumentsZip} // <--- UPDATE FUNCTION CALL
                  disabled={isGeneratingPdf} // Keep state check
                  className={`inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-blue-500 transition duration-150 ease-in-out ${isGeneratingPdf ? 'opacity-50 cursor-not-allowed' : ''}`}
                  title="Download All KYC Documents as ZIP" // <--- UPDATE TITLE
                >
                  {isGeneratingPdf ? (
                    <>
                      <svg className="animate-spin -ml-0.5 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Processing... {/* <--- UPDATE TEXT */}
                    </>
                  ) : (
                    <>
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                        {/* Using a folder/zip icon might be more appropriate */}
                        <path strokeLinecap="round" strokeLinejoin="round" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                      </svg>
                      Download Documents (ZIP) {/* <--- UPDATE TEXT */}
                    </>
                  )}
                </button>
                {/* --- END DOWNLOAD BUTTON --- */}

                {/* Existing Close Button */}
                <button
                  onClick={handleCloseModal}
                  disabled={isGeneratingPdf} // Disable close while generating? Optional.
                  className={`text-gray-400 hover:text-gray-600 p-1 rounded-full hover:bg-gray-100 transition-colors duration-150 ${isGeneratingPdf ? 'opacity-50 cursor-not-allowed' : ''}`}
                >
                  <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>
            {/* Modal Content Area */}
            <div className="flex-grow overflow-y-auto">
              {/* --- NEW Tab Navigation --- */}
              <div className="px-6 border-b border-gray-200 sticky bg-white z-10">
                <nav className="-mb-px flex space-x-4 overflow-x-auto" aria-label="Tabs">
                  {[
                    { key: 'businessDocs', label: 'Business Documents' },
                    { key: 'financials', label: 'Financials' },
                    { key: 'shareholdersPersonal', label: 'Shareholders' },
                    { key: 'buyers', label: 'Top Buyers' },
                    { key: 'review', label: 'EKYC and Summary' },
                    { key: 'verification', label: 'Overall Verification' }
                  ].map(tab => (
                    <button
                      key={tab.key}
                      className={`whitespace-nowrap py-3 px-2 border-b-2 font-medium text-sm transition-colors duration-150 ${activeTab === tab.key ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}
                      onClick={() => setActiveTab(tab.key)}
                    >
                      {tab.label}
                    </button>
                  ))}
                </nav>
              </div>

              {/* Tab Content Area */}
              <div className="p-4 sm:p-6 bg-gray-50"> {/* Added light bg to content */}

                {/* --- Tab 1: Business Documents --- */}
                {activeTab === 'businessDocs' && (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {businessDocFields.map(field => {
                      const docData = getNested(selectedUser, field.key, null);
                      // Determine if the document actually has an uploaded file (check for signedUrl or filePath)
                      const documentIsUploaded = docData && (docData.signedUrl || docData.filePath);

                      // Render ONLY if it's a required field, OR if it's an optional field that HAS been uploaded
                      if (field.isRequired || documentIsUploaded) {
                        return (
                          <RenderDocumentDisplay
                            key={field.key}
                            userId={selectedUser._id}
                            documentData={docData} // Pass the retrieved docData
                            documentFieldPath={field.key}
                            label={field.label}
                            isRequired={field.isRequired}
                            onUpdateSuccess={handleDocumentUpdateSuccess}
                          />
                        );
                      }
                      return null; // Don't render the card if it's optional and not uploaded
                    })}
                  </div>
                )}

                {/* --- Tab 2: Financials --- */}
                {activeTab === 'financials' && (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {financialDocFields.map(field => (
                      <RenderDocumentDisplay
                        key={field.key}
                        userId={selectedUser._id}
                        documentData={getNested(selectedUser, field.key, null)}
                        documentFieldPath={field.key}
                        label={field.label}
                        isRequired={field.isRequired}
                        onUpdateSuccess={handleDocumentUpdateSuccess}
                      />
                    ))}
                  </div>
                )}

                {/* --- Tab 3: Shareholders & Personal Info --- */}
                {activeTab === 'shareholdersPersonal' && (
                  <div className="space-y-8">
                    {(getNested(selectedUser, 'shareholders', []) || []).map((sh, index) => (
                      <div key={sh._id || index} className="bg-white rounded-xl shadow-md border border-gray-100 overflow-hidden">
                        {/* Shareholder Header */}
                        <div className="bg-gradient-to-r from-indigo-50 to-blue-50 p-5 border-b border-gray-200">
                          <div className="flex flex-wrap justify-between items-center gap-4">
                            {/* Shareholder Name and Send Email Button */}
                            <div className="flex items-center gap-x-4">
                              <div className="h-10 w-10 flex-shrink-0 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-600 font-semibold">
                                {index + 1}
                              </div>
                              <h3 className="text-lg font-semibold text-gray-800">
                                {getNested(sh, 'firstName', '')} {getNested(sh, 'lastName', '')}
                              </h3>

                              {/* --- NEW Send KYC Email Button --- */}
                              <div className="flex items-center space-x-2">
                                <button
                                  onClick={() => handleSendShareholderKycEmail(sh._id)}
                                  disabled={emailSendStatus.loadingId === sh._id}
                                  className={`inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md shadow-sm text-white transition-colors duration-150 ${emailSendStatus.loadingId === sh._id ? 'bg-gray-400 cursor-not-allowed' : 'bg-sky-600 hover:bg-sky-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sky-500'}`}
                                >
                                  {emailSendStatus.loadingId === sh._id ? 'Sending...' : (sh.kycEmailSent ? 'Send Email Again' : 'Send KYC Email')}
                                </button>
                                {sh.kycEmailSent && !emailSendStatus.error && <span className="text-xs text-gray-500 italic">Email already sent</span>}
                                {emailSendStatus.error && emailSendStatus.loadingId === sh._id && <span className="text-xs text-red-600">{emailSendStatus.error}</span>}
                              </div>
                            </div>

                            {/* Shareholder KYC Dropdown */}
                            <div className="flex items-center space-x-3">
                              <span className="text-sm font-medium text-gray-600">KYC Status:</span>
                              <div className="flex items-center">
                                <StatusBadge status={getNested(sh, 'kycVerificationStatus', 'INITIATED')} />
                                <select
                                  id={`shareholder-kyc-status-${index}`}
                                  value={getNested(sh, 'kycVerificationStatus', 'INITIATED')}
                                  onChange={(e) => handleShareholderKycUpdate(index, e.target.value)}
                                  disabled={updatingShareholderIndex === index}
                                  className={`
                    ml-3 text-sm font-medium rounded-lg border border-gray-300 shadow-sm 
                    focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500
                    py-1.5 pl-3 pr-8 transition-colors duration-200
                    ${updatingShareholderIndex === index
                                      ? 'opacity-60 cursor-not-allowed bg-gray-100'
                                      : 'bg-white hover:bg-gray-50'
                                    }
                  `}
                                >
                                  <option value="INITIATED">Initiated</option>
                                  <option value="REINITIATED">Reinitiated</option>
                                  <option value="UNDER_REVIEW">Under Review</option>
                                  <option value="REVIEW">Review Needed</option>
                                  <option value="INFO_NEEDED">Information Needed</option>
                                  <option value="APPROVED">Approved</option>
                                  <option value="REJECTED">Rejected</option>
                                </select>
                              </div>

                              {/* Loading Spinner */}
                              {updatingShareholderIndex === index && (
                                <div className="h-5 w-5 animate-spin rounded-full border-2 border-t-indigo-600 border-r-indigo-600 border-b-indigo-600 border-l-gray-300"></div>
                              )}

                              {/* Error Message */}
                              {shareholderUpdateError && updatingShareholderIndex === null && (
                                <p className="text-xs text-red-600 font-medium">Update failed</p>
                              )}
                            </div>
                          </div>
                        </div>

                        {/* Shareholder Content */}
                        <div className="p-5">
                          {/* Basic Info */}
                          <div className="mb-6">
                            <h4 className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-3">Personal Information</h4>
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                              {renderInfoItem("Middle Name", getNested(sh, 'middleName'), "user-circle")}
                              {renderInfoItem("Email", getNested(sh, 'email'), "mail")}
                              {renderInfoItem("Overall Notes", getNested(sh, 'verificationNotes'), "document-text")}
                            </div>
                          </div>

                          {/* Address */}
                          <div className="mb-6">
                            <h4 className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-3">Address Details</h4>
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                              {renderInfoItem("Zone", getNested(sh, 'address.zone'), "map-pin")}
                              {renderInfoItem("Street", getNested(sh, 'address.streetNo'), "map")}
                              {renderInfoItem("Building", getNested(sh, 'address.buildingNo'), "home")}
                              {renderInfoItem("Floor", getNested(sh, 'address.floorNo'), "stairs")}
                              {renderInfoItem("Unit", getNested(sh, 'address.unitNo'), "door-open")}
                            </div>
                          </div>

                          {/* Verification Statuses */}
                          <div className="mb-6">
                            <h4 className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-3">Verification Statuses</h4>
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">

                              {/* Overall KYC Status */}
                              {/* <div className="bg-white rounded-lg p-3 border border-gray-200 shadow-sm">
                                <span className="block text-gray-700 font-medium text-sm mb-1">Overall KYC Status</span>
                                <StatusDisplayControl
                                  label=""
                                  currentStatus={getNested(sh, 'kycVerificationStatus', 'INITIATED')}
                                  statusOptions={KYC_VERIFICATION_STATUS_OPTIONS}
                                  onStatusChange={(newStatus) => handleGenericStatusUpdate('shareholder', 'kycVerificationStatus', newStatus, index)}
                                  isLoading={specificStatusUpdate.loadingPath === `shareholders.${index}.kycVerificationStatus`}
                                  error={specificStatusUpdate.errorPath === `shareholders.${index}.kycVerificationStatus` ? specificStatusUpdate.errorMessage : null}
                                />
                              </div> */}

                              {/* AML Status */}
                              <div className="bg-white rounded-lg p-3 border border-gray-200 shadow-sm">
                                <span className="block text-gray-700 font-medium text-sm mb-1">AML Status</span>
                                <StatusDisplayControl
                                  label=""
                                  currentStatus={getNested(sh, 'amlStatus', 'NOT_CHECKED')}
                                  statusOptions={AML_STATUS_OPTIONS}
                                  onStatusChange={(newStatus) => handleGenericStatusUpdate('shareholder', 'amlStatus', newStatus, index)}
                                  isLoading={specificStatusUpdate.loadingPath === `shareholders.${index}.amlStatus`}
                                  error={specificStatusUpdate.errorPath === `shareholders.${index}.amlStatus` ? specificStatusUpdate.errorMessage : null}
                                />
                              </div>

                              {/* Video KYC Status */}
                              <div className="bg-white rounded-lg p-3 border border-gray-200 shadow-sm">
                                <span className="block text-gray-700 font-medium text-sm mb-1">Video KYC Status</span>
                                <StatusDisplayControl
                                  label=""
                                  currentStatus={getNested(sh, 'videoKyc.status', 'NOT_ATTEMPTED')}
                                  statusOptions={VIDEO_KYC_STATUS_OPTIONS}
                                  onStatusChange={(newStatus) => handleGenericStatusUpdate('shareholder', 'videoKyc.status', newStatus, index)}
                                  isLoading={specificStatusUpdate.loadingPath === `shareholders.${index}.videoKyc.status`}
                                  error={specificStatusUpdate.errorPath === `shareholders.${index}.videoKyc.status` ? specificStatusUpdate.errorMessage : null}
                                />
                              </div>

                              {/* Address Verification */}
                              <div className="bg-white rounded-lg p-3 border border-gray-200 shadow-sm">
                                <span className="block text-gray-700 font-medium text-sm mb-1">Address Verification</span>
                                <StatusDisplayControl
                                  label=""
                                  currentStatus={getNested(sh, 'addressVerification.status', 'NOT_ATTEMPTED')}
                                  statusOptions={ADDRESS_VERIFICATION_STATUS_OPTIONS}
                                  onStatusChange={(newStatus) => handleGenericStatusUpdate('shareholder', 'addressVerification.status', newStatus, index)}
                                  isLoading={specificStatusUpdate.loadingPath === `shareholders.${index}.addressVerification.status`}
                                  error={specificStatusUpdate.errorPath === `shareholders.${index}.addressVerification.status` ? specificStatusUpdate.errorMessage : null}
                                />
                              </div>

                              {/* Document Set KYC Status */}
                              {/* <div className="bg-white rounded-lg p-3 border border-gray-200 shadow-sm">
                                <span className="block text-gray-700 font-medium text-sm mb-1">Document Set KYC Status</span>
                                <StatusDisplayControl
                                  label=""
                                  currentStatus={getNested(sh, 'documentKycStatus', 'NOT_ATTEMPTED')}
                                  statusOptions={DOCUMENT_KYC_STATUS_OPTIONS}
                                  onStatusChange={(newStatus) => handleGenericStatusUpdate('shareholder', 'documentKycStatus', newStatus, index)}
                                  isLoading={specificStatusUpdate.loadingPath === `shareholders.${index}.documentKycStatus`}
                                  error={specificStatusUpdate.errorPath === `shareholders.${index}.documentKycStatus` ? specificStatusUpdate.errorMessage : null}
                                />
                              </div> */}

                            </div>
                          </div>

                          {/* Documents Section */}
                          <div>
                            <h4 className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-4">Documents</h4>
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                              <RenderDocumentDisplay
                                userId={selectedUser._id}
                                documentData={getNested(sh, 'passport', null)}
                                documentFieldPath={`shareholders.${index}.passport`}
                                label="Passport"
                                onUpdateSuccess={handleDocumentUpdateSuccess}
                              />
                              <RenderDocumentDisplay
                                userId={selectedUser._id}
                                documentData={getNested(sh, 'qid', null)}
                                documentFieldPath={`shareholders.${index}.qid`}
                                label="QID"
                                onUpdateSuccess={handleDocumentUpdateSuccess}
                              />
                              <RenderDocumentDisplay
                                userId={selectedUser._id}
                                documentData={getNested(sh, 'proofOfAddress', null)}
                                documentFieldPath={`shareholders.${index}.proofOfAddress`}
                                label="Proof of Address"
                                onUpdateSuccess={handleDocumentUpdateSuccess}
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                    {(getNested(selectedUser, 'shareholders', []) || []).length === 0 && (
                      <div className="bg-white rounded-lg shadow p-8 text-center">
                        <div className="inline-flex h-16 w-16 items-center justify-center rounded-full bg-gray-100 mb-4">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                          </svg>
                        </div>
                        <p className="text-gray-500 text-lg">No shareholders listed for this account.</p>
                      </div>
                    )}
                  </div>
                )}

                {/* --- Tab 4: Top Buyers --- */}
                {activeTab === 'buyers' && (
                  <div className="space-y-4">
                    {(getNested(selectedUser, 'kyc.buyers', []) || []).map((buyer, index) => (
                      <div key={buyer._id || index} className="bg-white p-4 rounded-lg shadow border border-gray-200">
                        {/* --- MODIFIED Buyer Header with Button --- */}
                        <div className="flex justify-between items-center mb-3 border-b pb-2">
                          <h3 className="text-lg font-semibold text-gray-800">
                            Buyer {index + 1}: {getNested(buyer, 'buyerName')}
                          </h3>
                          <div className="flex items-center space-x-2">
                            <button
                              onClick={() => handleSendBuyerInvitation(buyer.contactEmail, index)}
                              disabled={emailSendStatus.loadingId === `buyer-${index}`}
                              className={`inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md shadow-sm text-white transition-colors duration-150 ${emailSendStatus.loadingId === `buyer-${index}` ? 'bg-gray-400 cursor-not-allowed' : 'bg-sky-600 hover:bg-sky-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sky-500'}`}
                            >
                              {emailSendStatus.loadingId === `buyer-${index}` ? 'Sending...' : (buyer.buyerEmailSent ? 'Send Again' : 'Send Invitation')}
                            </button>
                            {buyer.buyerEmailSent && !emailSendStatus.error && <span className="text-xs text-gray-500 italic">Email already sent</span>}
                            {emailSendStatus.error && emailSendStatus.loadingId === `buyer-${index}` && <span className="text-xs text-red-600">{emailSendStatus.error}</span>}
                          </div>
                        </div>
                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-x-4 gap-y-1 mb-3">
                          {renderInfoItem("Full Name", getNested(buyer, 'buyerName'))}
                          {renderInfoItem("Contact Person", getNested(buyer, 'contactPerson'))}
                          {renderInfoItem("Contact Phone", getNested(buyer, 'contactPhone'))}
                          {renderInfoItem("Contact Email", getNested(buyer, 'contactEmail'))}
                          {/* {renderInfoItem("Business Type", getNested(buyer, 'businessType'))} */}
                          {renderInfoItem("Reg. Number", getNested(buyer, 'registrationNumber'))}
                          {/* {renderInfoItem("Annual Purchase Volume", getNested(buyer, 'annualPurchaseVolume'))} */}
                          {/* {renderInfoItem("Payment Terms", getNested(buyer, 'paymentTerms'))} */}
                        </div>
                        {getNested(buyer, 'companyDocument') && (
                          <div className="pt-3 border-t">
                            <RenderDocumentDisplay
                              userId={selectedUser._id}
                              documentData={getNested(buyer, 'companyDocument', null)}
                              documentFieldPath={`kyc.buyers.${index}.companyDocument`}
                              label="Company Document"
                              onUpdateSuccess={handleDocumentUpdateSuccess}
                            />
                          </div>
                        )}
                      </div>
                    ))}
                    {(getNested(selectedUser, 'kyc.buyers', []) || []).length === 0 && <p className="text-gray-500 italic">No buyers listed.</p>}
                  </div>
                )}

                {/* --- Tab 5: Review Other Info --- */}
                {activeTab === 'review' && (
                  <div className="space-y-6">
                    {/* Card for Basic Personal/Contact Info */}
                    {/* Assuming StatusBadge, getNested, and renderInfoItem are defined and available.
    Make sure your StatusBadge's getStatusClass function is updated to handle:
    - 'WITHIN_QATAR' (for green)
    - Statuses starting with 'LOCATED_IN_' (for red)
    - 'N_A' or a similar string for N/A geolocation data (for a neutral/gray badge)
*/}

                    <div className="bg-white p-4 rounded-lg shadow border border-gray-200">
                      <h3 className="text-lg font-semibold text-gray-800 mb-3 border-b pb-2">Basic, Contact &amp; Geolocation Info (Primary Applicant)</h3>
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-x-4 gap-y-2"> {/* Increased gap-y slightly for better spacing */}
                        {renderInfoItem("First Name", getNested(selectedUser, 'firstName'))}
                        {renderInfoItem("Middle Name", getNested(selectedUser, 'middleName'))}
                        {renderInfoItem("Last Name", getNested(selectedUser, 'lastName'))}
                        {renderInfoItem("Email", getNested(selectedUser, 'email'))}
                        {renderInfoItem("Mobile Number", getNested(selectedUser, 'mobileNo'))}

                        {/* Geolocation Logic and Display Start Here */}
                        {(() => {
                          const geolocationData = getNested(selectedUser, 'commercialRegistration.shuftiCallbackData.info.geolocation');
                          let locationStatusForBadge = 'N_A'; // Default for N/A data
                          let countryNameDisplay = 'N/A';
                          let countryCodeDisplay = 'N/A';
                          let regionNameDisplay = 'N/A';
                          let regionCodeDisplay = 'N/A';
                          let cityDisplay = 'N/A';
                          let ipDisplay = 'N/A';

                          if (geolocationData) {
                            const countryName = geolocationData.country_name || '';
                            const countryCode = geolocationData.country_code || '';

                            countryNameDisplay = countryName || 'N/A';
                            countryCodeDisplay = countryCode || 'N/A';
                            regionNameDisplay = geolocationData.region_name || 'N/A';
                            regionCodeDisplay = geolocationData.region_code || 'N/A';
                            cityDisplay = geolocationData.city || 'N/A';
                            ipDisplay = geolocationData.ip || 'N/A';

                            if (countryName.toLowerCase() === 'qatar' || countryCode.toUpperCase() === 'QA') {
                              locationStatusForBadge = 'QATAR';
                            } else {
                              const countryIdentifier = (countryName || 'UNKNOWN_COUNTRY').toUpperCase().replace(/[^A-Z0-9_]/g, '_');
                              locationStatusForBadge = `LOCATED IN ${countryIdentifier}`;
                            }
                          }

                          return (
                            <>
                              {/* Custom styled div for Geolocation Status */}
                              <div className="bg-gray-50 rounded-md border border-gray-200 p-3 flex items-center justify-between sm:col-span-1 lg:col-span-1">
                                <div className="text-sm text-gray-700 font-medium">CR Geo Status:</div>
                                <StatusBadge status={locationStatusForBadge} />
                              </div>

                              {renderInfoItem("CR Geo Country", countryNameDisplay)}
                              {renderInfoItem("CR Geo Country Code", countryCodeDisplay)}
                              {renderInfoItem("CR Geo Region", regionNameDisplay)}
                              {renderInfoItem("CR Geo Region Code", regionCodeDisplay)}
                              {renderInfoItem("CR Geo City", cityDisplay)}
                              {renderInfoItem("CR Geo IP", ipDisplay)}
                            </>
                          );
                        })()}
                        {/* Geolocation Logic and Display End Here */}

                      </div>
                    </div>

                    {/* Card for KYC Business Details ... */}
                    <div className="bg-white p-6 rounded-lg shadow border border-gray-200">
                      <h3 className="text-xl font-semibold text-gray-800 mb-5 border-b pb-3">
                        Applicant & Business Verification Statuses
                      </h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {/* Applicant AML Status */}
                        <div className="bg-gray-50 rounded-md border border-gray-200 p-4 flex items-center justify-between">
                          <div className="text-gray-700 font-medium">Business AML Status:</div>
                          <StatusDisplayControl
                            label=""
                            currentStatus={getNested(selectedUser, 'kyc.amlStatus', 'NOT_CHECKED')}
                            statusOptions={AML_STATUS_OPTIONS}
                            onStatusChange={(newStatus) => handleGenericStatusUpdate('user', 'amlStatus', newStatus)}
                            isLoading={specificStatusUpdate.loadingPath === 'kyc.amlStatus'}
                            error={specificStatusUpdate.errorPath === 'kyc.amlStatus' ? specificStatusUpdate.errorMessage : null}
                          />
                        </div>

                        {/* Applicant Video KYC */}
                        {/* <div className="bg-gray-50 rounded-md border border-gray-200 p-4 flex items-center justify-between">
                          <div className="text-gray-700 font-medium">Applicant Video KYC:</div>
                          <StatusDisplayControl
                            label=""
                            currentStatus={getNested(selectedUser, 'kyc.videoKyc.status', 'NOT_ATTEMPTED')}
                            statusOptions={VIDEO_KYC_STATUS_OPTIONS}
                            onStatusChange={(newStatus) => handleGenericStatusUpdate('user', 'videoKyc.status', newStatus)}
                            isLoading={specificStatusUpdate.loadingPath === 'kyc.videoKyc.status'}
                            error={specificStatusUpdate.errorPath === 'kyc.videoKyc.status' ? specificStatusUpdate.errorMessage : null}
                          />
                        </div> */}

                        {/* Business Address Verification */}
                        <div className="bg-gray-50 rounded-md border border-gray-200 p-4 flex items-center justify-between">
                          <div className="text-gray-700 font-medium">Business Address Verification:</div>
                          <StatusDisplayControl
                            label=""
                            currentStatus={getNested(selectedUser, 'kyc.businessDetails.businessAddressVerification.status', 'INITIATED')}
                            statusOptions={BUSINESS_ADDRESS_VERIFICATION_STATUS_OPTIONS}
                            onStatusChange={(newStatus) => handleGenericStatusUpdate('user', 'businessDetails.businessAddressVerification.status', newStatus)}
                            isLoading={specificStatusUpdate.loadingPath === 'kyc.businessDetails.businessAddressVerification.status'}
                            error={specificStatusUpdate.errorPath === 'kyc.businessDetails.businessAddressVerification.status' ? specificStatusUpdate.errorMessage : null}
                          />
                        </div>

                        {/* KYB Verification Status (CR) with Initiate Button */}
                        <div className="bg-gray-50 rounded-md border border-gray-200 p-4 flex items-center justify-between gap-4">
                          <div className="flex flex-col">
                            <span className="text-gray-700 font-medium">KYB Verification Status (CR):</span>
                            <StatusBadge status={getNested(selectedUser, 'commercialRegistration.verificationStatus', 'Not Available')} />
                          </div>

                          {/* Conditionally render the button ONLY if status is SUBMITTED */}
                          {getNested(selectedUser, 'commercialRegistration.verificationStatus') === 'SUBMITTED' && (
                            <button
                              onClick={handleInitiateKybVerification}
                              disabled={isInitiatingKyb}
                              className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white transition-colors duration-150 ${isInitiatingKyb
                                ? 'bg-gray-400 cursor-not-allowed'
                                : 'bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500'
                                }`}
                            >
                              {isInitiatingKyb ? (
                                <>
                                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                  </svg>
                                  Initiating...
                                </>
                              ) : (
                                'Initiate KYB'
                              )}
                            </button>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Card for KYC Employment/Income */}
                    <div className="bg-white p-4 rounded-lg shadow border border-gray-200">
                      <h3 className="text-lg font-semibold text-gray-800 mb-3 border-b pb-2">Bank Details</h3>
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-x-4 gap-y-1 mb-3">
                        {/* Employment */}
                        {/* {renderInfoItem("Employer Name", getNested(selectedUser, 'kyc.employmentDetails.employerName'))}
                        {renderInfoItem("Position", getNested(selectedUser, 'kyc.employmentDetails.position'))}
                        {renderInfoItem("Work ID", getNested(selectedUser, 'kyc.employmentDetails.workId'))}
                        {renderInfoItem("Monthly Income", getNested(selectedUser, 'kyc.incomeDetails.monthlyIncome'))}
                        {renderInfoItem("Monthly Expenses", getNested(selectedUser, 'kyc.incomeDetails.monthlyExpenses'))}
                        {renderInfoItem("Source of Wealth", getNested(selectedUser, 'kyc.incomeDetails.sourceOfWealth'))} */}
                        {renderInfoItem("Account Number", getNested(selectedUser, 'kyc.incomeDetails.accountNumber'))}
                        {renderInfoItem("IFSC Code", getNested(selectedUser, 'kyc.incomeDetails.ifscCode'))}
                      </div>
                      {/* Employment/Income Docs */}
                      {/* <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 pt-3 border-t">
                        <RenderDocumentDisplay userId={selectedUser._id} documentData={getNested(selectedUser, 'kyc.employmentDetails.employmentLetter')} documentFieldPath="kyc.employmentDetails.employmentLetter" label="Employment Letter" onUpdateSuccess={handleDocumentUpdateSuccess} />
                        <RenderDocumentDisplay userId={selectedUser._id} documentData={getNested(selectedUser, 'kyc.incomeDetails.proofOfIncome')} documentFieldPath="kyc.incomeDetails.proofOfIncome" label="Proof of Income" onUpdateSuccess={handleDocumentUpdateSuccess} />
                      </div> */}
                    </div>

                    {/* Card for KYC Business Details (that weren't in Business tab) */}
                    <div className="bg-white p-4 rounded-lg shadow border border-gray-200">
                      <h3 className="text-lg font-semibold text-gray-800 mb-3 border-b pb-2">KYB and Business Details</h3>
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-x-4 gap-y-1">
                        {renderInfoItem("Legal Entity Name", getNested(selectedUser, 'kyc.businessDetails.businessName'))}
                        {renderInfoItem("CR Number", getNested(selectedUser, 'kyc.businessDetails.crNumber'))}
                        {renderInfoItem("KYB Verification Status", getNested(selectedUser, 'commercialRegistration.verificationStatus'))}
                        {renderInfoItem("AML Verification Status", getNested(selectedUser, 'commercialRegistration.shuftiCallbackData.verification_result.aml_for_businesses'))}
                        {renderInfoItem("Business Address Verification Status", getNested(selectedUser, 'kyc.businessDetails.businessAddressVerification.status'))}
                        {renderInfoItem("Video KYC Verification Status", getNested(selectedUser, 'kyc.videoKyc.status'))}
                        {renderInfoItem("Based in Qatar", getNested(selectedUser, 'kyc.isQatarBased'))}
                        {renderInfoItem("Business Name", getNested(selectedUser, 'kyc.businessDetails.businessName'))}
                        {renderInfoItem("Business Type", getNested(selectedUser, 'kyc.businessDetails.businessType'))}
                        {/* {renderInfoItem("Ownership Type", getNested(selectedUser, 'kyc.businessDetails.ownershipType'))} */}
                        {renderInfoItem("Sector", getNested(selectedUser, 'kyc.businessDetails.sector'))}
                        {/* {renderInfoItem("Year Established", getNested(selectedUser, 'kyc.businessDetails.yearEstablished'))} */}
                        {/* {renderInfoItem("Monthly Turnover", getNested(selectedUser, 'kyc.businessDetails.monthlyTurnover'))} */}
                        {/* {renderInfoItem("Nature of Business", getNested(selectedUser, 'kyc.businessDetails.natureOfBusiness'))} */}
                        {/* Business Address */}
                        {renderInfoItem("Business Addr L1", getNested(selectedUser, 'kyc.businessDetails.businessAddressLine1'))}
                        {renderInfoItem("Business Addr L2", getNested(selectedUser, 'kyc.businessDetails.businessAddressLine2'))}
                        {renderInfoItem("Business City", getNested(selectedUser, 'kyc.businessDetails.businessCity'))}
                        {renderInfoItem("Business Country", getNested(selectedUser, 'kyc.businessDetails.businessCountry'))}
                      </div>
                    </div>

                    {/* Card for Directors (Info + Docs) */}
                    {(getNested(selectedUser, 'kyc.directors', []) || []).length > 0 && (
                      <div className="bg-white p-4 rounded-lg shadow border border-gray-200">
                        <h3 className="text-lg font-semibold text-gray-800 mb-3 border-b pb-2">Directors</h3>
                        <div className="space-y-4">
                          {(selectedUser.kyc.directors || []).map((dir, index) => (
                            <div key={dir._id || index} className="border rounded p-3 bg-gray-50">
                              <p className="text-base font-medium text-gray-800 mb-2">Director {index + 1}: {getNested(dir, 'directorName')}</p>
                              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-x-4 gap-y-1 mb-3">
                                {/* Director Info */}
                                {renderInfoItem("Position", getNested(dir, 'position'))}
                                {renderInfoItem("Nationality", getNested(dir, 'nationality'))}
                                {renderInfoItem("DOB", getNested(dir, 'dateOfBirth'))}
                                {renderInfoItem("National ID", getNested(dir, 'nationalId'))}
                                {renderInfoItem("Contact Person", getNested(dir, 'contactPerson'))}
                                {renderInfoItem("Contact Phone", getNested(dir, 'contactPhone'))}
                                {renderInfoItem("Contact Email", getNested(dir, 'contactEmail'))}
                                {renderInfoItem("Is UBO", getNested(dir, 'isUBO'))}
                                {renderInfoItem("Auth Signatory", getNested(dir, 'isAuthorizedSignatory'))}
                                {renderInfoItem("Shareholding %", getNested(dir, 'shareholdingPercentage') !== undefined ? `${getNested(dir, 'shareholdingPercentage')}%` : 'N/A')}
                                {renderInfoItem("Address", getNested(dir, 'directorAddress'))}
                              </div>
                              {/* Director Document */}
                              {getNested(dir, 'idDocument') && (
                                <div className="pt-3 border-t">
                                  <RenderDocumentDisplay userId={selectedUser._id} documentData={getNested(dir, 'idDocument')} documentFieldPath={`kyc.directors.${index}.idDocument`} label="ID Document" onUpdateSuccess={handleDocumentUpdateSuccess} />
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Card for Signatories / Owners (Info + Docs) */}


                    {/* Card for Other System Info/Flags */}
                    {/* <div className="bg-white p-4 rounded-lg shadow border border-gray-200">
                      <h3 className="text-lg font-semibold text-gray-800 mb-3 border-b pb-2">System Info & Flags</h3>
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-x-4 gap-y-1">
                        {renderInfoItem("Bank Stmt Uploaded Flag", getNested(selectedUser, 'isBankStatementUploaded'))}
                        {renderInfoItem("First Invoice Uploaded Flag", getNested(selectedUser, 'isFirstInvoiceUploaded'))}
                        {renderInfoItem("Credit Line Assigned Flag", getNested(selectedUser, 'isCreditLineAssigned'))}
                        {renderInfoItem("Inserted On", getNested(selectedUser, 'insertedOn'))}
                        {renderInfoItem("Last Updated On", getNested(selectedUser, 'updatedOn'))}
                      </div>
                    </div> */}

                    {/* Card for Additional Invoice Docs */}
                    {/* <div className="bg-white p-4 rounded-lg shadow border border-gray-200">
                      <h3 className="text-lg font-semibold text-gray-800 mb-3 border-b pb-2">Additional Invoice Docs</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <RenderDocumentDisplay userId={selectedUser._id} documentData={getNested(selectedUser, 'invoiceAdditionalDoc1')} documentFieldPath="invoiceAdditionalDoc1" label="Invoice Addl Doc 1" onUpdateSuccess={handleDocumentUpdateSuccess} />
                        <RenderDocumentDisplay userId={selectedUser._id} documentData={getNested(selectedUser, 'invoiceAdditionalDoc2')} documentFieldPath="invoiceAdditionalDoc2" label="Invoice Addl Doc 2" onUpdateSuccess={handleDocumentUpdateSuccess} />
                      </div>
                    </div> */}
                  </div>
                )}

                {/* --- Tab 6: Overall Verification --- */}
                {activeTab === 'verification' && (
                  <div className="max-w-2xl mx-auto">
                    <div className="bg-white p-6 rounded-lg shadow border border-gray-200">
                      <h3 className="text-xl font-semibold text-gray-900 mb-6 text-center">Overall KYC Verification Action</h3>
                      {/* Current Status Display */}
                      <div className="mb-6 text-center">
                        <label className="block text-sm font-medium text-gray-700 mb-2">Current Overall Status</label>
                        <StatusBadge status={overallVerificationStatus} />
                      </div>
                      {/* Status Update Dropdown */}
                      <div className="mb-6">
                        <label htmlFor="overallStatusSelect" className="block text-sm font-medium text-gray-700 mb-1">Update Overall Status</label>
                        <select
                          id="overallStatusSelect"
                          value={overallVerificationStatus}
                          onChange={(e) => setOverallVerificationStatus(e.target.value)}
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm py-2 px-3"
                        >
                          {/* Options remain the same */}
                          <option value="INITIATED">Initiated</option>
                          <option value="REINITIATED">Reinitiated</option>
                          <option value="UNDER_REVIEW">Under Review</option>
                          <option value="REVIEW">Review Needed</option>
                          <option value="INFO_NEEDED">Information Needed</option>
                          <option value="APPROVED">Approved</option>
                          <option value="REJECTED">Rejected</option>
                        </select>
                      </div>
                      {/* Notes Textarea */}
                      <div className="mb-6">
                        <label htmlFor="overallNotesText" className="block text-sm font-medium text-gray-700 mb-1">Overall Verification Notes (Internal)</label>
                        <textarea
                          id="overallNotesText"
                          value={overallVerificationNotes}
                          onChange={(e) => setOverallVerificationNotes(e.target.value)}
                          rows={5}
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm py-2 px-3"
                          placeholder="Add overall verification notes here..."
                        />
                      </div>
                      {/* Update Button */}
                      <button
                        onClick={handleUpdateOverallKyc}
                        disabled={isUpdating}
                        className={`w-full inline-flex justify-center items-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white ${isUpdating ? 'bg-gray-400' : 'bg-[#004141] hover:bg-[#002D2D]'
                          } focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-150 ease-in-out`}
                      >
                        {isUpdating ? (
                          <>
                            <svg className="animate-spin h-5 w-5 mr-2 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"></path>
                            </svg>
                            Updating...
                          </>
                        ) : (
                          'Update Overall KYC Status & Notes'
                        )}
                      </button>

                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Modal Footer: Padding, border, background, flex layout */}
            <div className="px-6 py-4 border-t border-gray-200 bg-gray-50 flex justify-end space-x-3 sticky bottom-0 flex-shrink-0">
              <button
                onClick={handleCloseModal} // <-- CHANGE HERE
                type="button" // Explicit type
                className="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-150"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}