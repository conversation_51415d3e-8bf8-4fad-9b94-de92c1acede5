import React from 'react';

/**
 * Helper function to format phone numbers with proper display and clickable links
 * @param {string|number} phoneNumber - The phone number to format
 * @returns {JSX.Element} - Formatted phone number with clickable tel: link
 */
export const formatPhoneNumber = (phoneNumber) => {
  if (!phoneNumber || phoneNumber === 'N/A' || phoneNumber === '') {
    return <span className="text-gray-500 italic">Not Provided</span>;
  }

  // Clean the phone number (remove spaces, dashes, parentheses)
  const cleanNumber = phoneNumber.toString().replace(/[\s\-\(\)]/g, '');
  
  // Check if it already starts with +
  let formattedNumber = cleanNumber;
  if (!cleanNumber.startsWith('+')) {
    // If it doesn't start with +, assume it's a Qatar number and add +974
    if (cleanNumber.startsWith('974')) {
      formattedNumber = '+' + cleanNumber;
    } else if (cleanNumber.length === 8) {
      // Qatar mobile numbers are typically 8 digits
      formattedNumber = '+974 ' + cleanNumber;
    } else {
      // For other cases, just add + at the beginning
      formattedNumber = '+' + cleanNumber;
    }
  } else {
    // If it already starts with +, format it nicely
    if (formattedNumber.startsWith('+974')) {
      const numberPart = formattedNumber.substring(4);
      formattedNumber = '+974 ' + numberPart;
    }
  }

  // Create the tel: link for calling
  const telLink = 'tel:' + cleanNumber.replace(/^\+/, '');

  return (
    <a 
      href={telLink}
      className="text-blue-600 hover:text-blue-800 hover:underline cursor-pointer inline-flex items-center"
      title={`Call ${formattedNumber}`}
    >
      <svg 
        xmlns="http://www.w3.org/2000/svg" 
        className="h-4 w-4 mr-1" 
        fill="none" 
        viewBox="0 0 24 24" 
        stroke="currentColor"
      >
        <path 
          strokeLinecap="round" 
          strokeLinejoin="round" 
          strokeWidth={2} 
          d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" 
        />
      </svg>
      {formattedNumber}
    </a>
  );
};

export default formatPhoneNumber;
